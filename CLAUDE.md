# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Development Server
- `npm run dev` - Starts development server with HMR on port 3001
  - Runs webpack dev server on port 8099 for HMR
  - Uses node<PERSON> to watch build folder and restart server
  - Client HMR via webpack-hot-middleware

### Build Commands  
- `npm run build` - Production build (same as build:online)
- `npm run build:online` - Build for online environment
- `npm run build:beta` - Build for beta environment  
- `npm run build:test1` - Build for test1 environment

### Production Deployment
- `npm start` - Starts production server using PM2 cluster mode on port 3009
- `npm run mock` - Starts JSON server mock API on port 8007

## Architecture Overview

This is a custom React SSR (Server-Side Rendering) application built for high performance scenarios (40W QPS capability mentioned in README). The architecture separates client and server builds with sophisticated streaming SSR.

### Key Technologies
- **Server**: Koa.js with custom SSR implementation
- **Client**: React 18 with React Router v6
- **State Management**: TanStack React Query for data fetching
- **Styling**: Styled Components + Less + Tailwind CSS
- **Code Splitting**: Loadable Components
- **Build**: Webpack 5 with separate client/server configs
- **Deployment**: PM2 cluster mode, supports serverless (AWS Lambda)

### Project Structure
```
app/
├── client/           # Client-side entry point
├── server/           # SSR server implementation
│   ├── server.ts     # Koa server setup
│   ├── app.tsx       # React app with query prefetching
│   ├── html.tsx      # HTML template rendering
│   └── stream/       # Streaming SSR utilities
src/
├── apis/             # API layer with React Query
├── pages/            # Route components
└── routes/           # Route configuration
config/
├── webpack.config.js # Base webpack configuration
├── webpack.dev.js    # Development webpack config
├── webpack.prod.js   # Production webpack config
└── env/              # Environment-specific variables
```

### SSR Implementation Details

The SSR system uses:
- **Route-based data prefetching**: Routes can define `queryKey` and `loadData` for server-side data fetching
- **Streaming rendering**: Uses `renderToStream` for improved performance
- **Styled Components SSR**: Server-side style extraction
- **Loadable Components**: Code splitting with SSR support
- **React Helmet**: Head management for SEO

### Development Workflow

1. **Client Development**: Hot reloading via webpack-dev-middleware on port 8099
2. **Server Development**: Nodemon watches build folder, restarts on server changes
3. **Dual Compilation**: Webpack compiles both client and server bundles simultaneously

### Environment Configuration

Environment-specific configs in `config/env/`:
- `local` - Development environment
- `online` - Production environment  
- `beta` - Beta environment
- `test1` - Test environment

### Build Outputs

- **Client**: `build/client/` - Static assets served at `/static/client/`
- **Server**: `build/server.js` - Node.js server bundle
- **Serverless**: `build/serverless.js` - AWS Lambda handler

### Performance Features

- **Thread Loader**: Multi-threaded TypeScript/Babel compilation
- **Filesystem Caching**: Webpack cache for faster rebuilds
- **Code Splitting**: Automatic chunk splitting with Loadable Components
- **CSS Extraction**: Separate CSS bundles in production
- **Asset Optimization**: Image optimization and SVG processing

### TypeScript Configuration

Uses `tsconfig-paths-webpack-plugin` for path mapping. Resolve modules from:
- `src/` directory
- `app/` directory  
- `node_modules/`

### Linting

ESLint runs during webpack compilation with:
- Fail on error (not warnings)
- Multi-threaded linting
- Dirty modules only in development