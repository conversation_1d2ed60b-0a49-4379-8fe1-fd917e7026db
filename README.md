# React Custom SSR

一个高性能的自定义 React 服务端渲染（SSR）解决方案，专为大规模应用和高并发场景设计。

## 🚀 性能指标

- **高并发支持**: 40W QPS (Query Per Second)
- **部署规模**: 500台服务器 (16G 8C Docker)
- **响应时间**: 100-150ms
- **核心特性**: 高性能、高可用、扩展性、安全性

## 📋 SSR 优势

- **SEO 友好**: 服务端渲染提供更好的搜索引擎优化
- **首屏性能**: 更快的首屏加载速度

## 🎯 适用场景

### 何时选择自定义 SSR

> **原则**: Next.js 能满足就用 Next.js，不能满足就自定义

### 适合自定义 SSR 的场景

- **高并发需求**: 需要处理大量并发请求
- **性能要求严格**: Next.js 冷启动 4-5s (Next.js 13+)
    - 框架代码量大且重
    - 通用性带来的性能开销
- **复杂大型项目**:
    - 微服务架构 (Module Federation)
    - 分布式并发渲染 (如淘宝、Shopee)
- **高可用要求**:
    - 服务降级到 CSR
    - 容错机制
    - 熔断保护 ([opossum](https://www.npmjs.com/package/opossum))

## 🏗️ 技术架构

### 1. 项目构建

支持多种构建工具：Webpack / Vite / Rollup

#### 服务端代码 (Node.js)
- **热更新**: Nodemon 监听文件变化

#### 客户端代码
- **热更新**: webpack-dev-server + webpack-hot-middleware

### 2. HTTP 服务

- **主要方案**: Koa.js
- **Serverless**: Cloudflare Worker 支持

### 3. 应用层

#### 数据预取
- **CSR & SSR**: React Query 统一数据管理

#### 路由系统
- **路由库**: React Router DOM

#### 中间件 (Koa Middleware)
- 日志收集
- 重定向处理 (301/302)

## ☁️ Serverless 部署

支持边缘渲染 + 分布式并发渲染架构

---

## 🚀 快速开始

详细的开发和部署指南请参考 [start.md](./start.md)

## 📚 更多文档

- [开发指南](./start.md) - 完整的项目启动和开发指南
- [Claude 开发说明](./CLAUDE.md) - AI 辅助开发的项目说明
