{"name": "react-custom-ssr", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "node scripts/dev.js & nodemon build/server.js", "build": "webpack build -c ./config/webpack.prod.js --env goal=online", "build:online": "webpack build --config ./config/webpack.prod.js --env goal=online", "build:beta": "webpack build --config ./config/webpack.prod.js --env goal=beta", "build:test1": "webpack build --config ./config/webpack.prod.js --env goal=test1", "start": "pm2 start ./ecosystem.config.js", "mock": "json-server --watch ./mocks/data.json --port 8007"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@koa/router": "^12.0.0", "@loadable/component": "^5.15.3", "@loadable/server": "^5.15.3", "@tanstack/react-query": "^4.29.3", "@vendia/serverless-express": "^4.10.4", "axios": "^1.3.6", "core-js": "^3.30.1", "koa": "^2.14.1", "koa-create-context": "^1.0.2", "process": "^0.11.10", "qs": "^6.11.1", "rc-dropdown": "^4.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^1.3.0", "react-intl": "^6.4.0", "react-is": "^18.2.0", "react-router-dom": "^6.10.0", "serverless-http": "^3.2.0", "styled-components": "^5.3.9"}, "devDependencies": {"@babel/core": "^7.21.4", "@babel/plugin-transform-react-jsx": "^7.21.0", "@babel/preset-env": "^7.21.4", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "7.18.6", "@loadable/babel-plugin": "^5.15.3", "@loadable/webpack-plugin": "^5.15.2", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.10", "@principalstudio/html-webpack-inject-preload": "^1.2.7", "@svgr/webpack": "^6.5.1", "@types/aws-lambda": "^8.10.119", "@types/koa": "^2.13.6", "@types/koa-mount": "^4.0.2", "@types/koa-static": "^4.0.2", "@types/koa__router": "^12.0.0", "@types/loadable__component": "^5.13.4", "@types/loadable__server": "^5.12.6", "@types/node": "^18.15.11", "@types/qs": "^6.9.7", "@types/react": "^18.3.2", "@types/react-dom": "^18.3.0", "@types/styled-components": "^5.1.26", "@types/webpack-dev-middleware": "^5.3.0", "@types/webpack-hot-middleware": "^2.25.6", "aws-cdk-lib": "^2.86.0", "aws-lambda": "^1.0.7", "babel-loader": "^9.1.2", "babel-plugin-styled-components": "^2.1.1", "babel-plugin-transform-commonjs": "^1.1.6", "chalk": "4.1.2", "circular-dependency-plugin": "^5.2.2", "constructs": "^10.2.69", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.7.3", "css-minimizer-webpack-plugin": "^4.2.2", "eslint": "^7.32.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-webpack-plugin": "^4.0.0", "express": "^4.18.2", "file-loader": "^6.2.0", "fork-ts-checker-webpack-plugin": "^7.3.0", "friendly-errors-webpack-plugin": "^1.7.0", "html-webpack-plugin": "^5.5.0", "ignore-loader": "^0.1.2", "json-server": "^0.17.3", "koa-mount": "^4.0.0", "koa-static": "^5.0.0", "less": "^4.1.3", "less-loader": "^11.1.0", "lodash": "^4.17.21", "mini-css-extract-plugin": "^2.7.5", "minimist": "^1.2.8", "nodemon": "^3.1.0", "null-loader": "^4.0.1", "pm2": "^5.3.0", "postcss-loader": "^7.2.4", "postcss-preset-env": "^7.8.3", "react-refresh": "^0.14.0", "simple-progress-webpack-plugin": "^1.1.2", "style-resources-loader": "^1.5.0", "tailwindcss": "^3.3.1", "terser-webpack-plugin": "^5.3.7", "thread-loader": "^3.0.4", "tsconfig-paths-webpack-plugin": "^4.0.1", "typescript": "^5.0.3", "webpack": "^5.78.0", "webpack-build-notifier": "^2.3.0", "webpack-bundle-analyzer": "^4.8.0", "webpack-cli": "^5.0.1", "webpack-dev-middleware": "^6.0.2", "webpack-dev-server": "^4.13.2", "webpack-hot-middleware": "^2.25.3", "webpack-manifest-plugin": "^5.0.0", "webpack-merge": "^5.8.0", "webpack-node-externals": "^3.0.0", "webpackbar": "^5.0.2"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}