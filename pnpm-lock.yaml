lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@koa/router':
        specifier: ^12.0.0
        version: 12.0.0
      '@loadable/component':
        specifier: ^5.15.3
        version: 5.15.3(react@18.3.1)
      '@loadable/server':
        specifier: ^5.15.3
        version: 5.15.3(@loadable/component@5.15.3)(react@18.3.1)
      '@tanstack/react-query':
        specifier: ^4.29.3
        version: 4.29.3(react-dom@18.3.1)(react@18.3.1)
      '@vendia/serverless-express':
        specifier: ^4.10.4
        version: 4.10.4
      axios:
        specifier: ^1.3.6
        version: 1.3.6
      core-js:
        specifier: ^3.30.1
        version: 3.30.1
      koa:
        specifier: ^2.14.1
        version: 2.14.1
      koa-create-context:
        specifier: ^1.0.2
        version: 1.0.2(@types/koa@2.13.6)(koa@2.14.1)
      process:
        specifier: ^0.11.10
        version: 0.11.10
      qs:
        specifier: ^6.11.1
        version: 6.11.1
      rc-dropdown:
        specifier: ^4.0.1
        version: 4.0.1(react-dom@18.3.1)(react@18.3.1)
      react:
        specifier: ^18.3.1
        version: 18.3.1
      react-dom:
        specifier: ^18.3.1
        version: 18.3.1(react@18.3.1)
      react-helmet-async:
        specifier: ^1.3.0
        version: 1.3.0(react-dom@18.3.1)(react@18.3.1)
      react-intl:
        specifier: ^6.4.0
        version: 6.4.0(react@18.3.1)(typescript@5.0.3)
      react-is:
        specifier: ^18.2.0
        version: 18.2.0
      react-router-dom:
        specifier: ^6.10.0
        version: 6.10.0(react-dom@18.3.1)(react@18.3.1)
      serverless-http:
        specifier: ^3.2.0
        version: 3.2.0
      styled-components:
        specifier: ^5.3.9
        version: 5.3.9(react-dom@18.3.1)(react-is@18.2.0)(react@18.3.1)
    devDependencies:
      '@babel/core':
        specifier: ^7.21.4
        version: 7.21.4
      '@babel/plugin-transform-react-jsx':
        specifier: ^7.21.0
        version: 7.21.0(@babel/core@7.21.4)
      '@babel/preset-env':
        specifier: ^7.21.4
        version: 7.21.4(@babel/core@7.21.4)
      '@babel/preset-react':
        specifier: ^7.18.6
        version: 7.18.6(@babel/core@7.21.4)
      '@babel/preset-typescript':
        specifier: 7.18.6
        version: 7.18.6(@babel/core@7.21.4)
      '@loadable/babel-plugin':
        specifier: ^5.15.3
        version: 5.15.3(@babel/core@7.21.4)
      '@loadable/webpack-plugin':
        specifier: ^5.15.2
        version: 5.15.2(webpack@5.78.0)
      '@pmmmwh/react-refresh-webpack-plugin':
        specifier: ^0.5.10
        version: 0.5.10(react-refresh@0.14.0)(webpack-dev-server@4.13.2)(webpack-hot-middleware@2.25.3)(webpack@5.78.0)
      '@principalstudio/html-webpack-inject-preload':
        specifier: ^1.2.7
        version: 1.2.7(html-webpack-plugin@5.5.0)(webpack@5.78.0)
      '@svgr/webpack':
        specifier: ^6.5.1
        version: 6.5.1
      '@types/aws-lambda':
        specifier: ^8.10.119
        version: 8.10.119
      '@types/koa':
        specifier: ^2.13.6
        version: 2.13.6
      '@types/koa-mount':
        specifier: ^4.0.2
        version: 4.0.2
      '@types/koa-static':
        specifier: ^4.0.2
        version: 4.0.2
      '@types/koa__router':
        specifier: ^12.0.0
        version: 12.0.0
      '@types/loadable__component':
        specifier: ^5.13.4
        version: 5.13.4
      '@types/loadable__server':
        specifier: ^5.12.6
        version: 5.12.6
      '@types/node':
        specifier: ^18.15.11
        version: 18.15.11
      '@types/qs':
        specifier: ^6.9.7
        version: 6.9.7
      '@types/react':
        specifier: ^18.3.2
        version: 18.3.2
      '@types/react-dom':
        specifier: ^18.3.0
        version: 18.3.0
      '@types/styled-components':
        specifier: ^5.1.26
        version: 5.1.26
      '@types/webpack-dev-middleware':
        specifier: ^5.3.0
        version: 5.3.0(webpack@5.78.0)
      '@types/webpack-hot-middleware':
        specifier: ^2.25.6
        version: 2.25.6(webpack-cli@5.0.1)
      aws-cdk-lib:
        specifier: ^2.86.0
        version: 2.86.0(constructs@10.2.69)
      aws-lambda:
        specifier: ^1.0.7
        version: 1.0.7
      babel-loader:
        specifier: ^9.1.2
        version: 9.1.2(@babel/core@7.21.4)(webpack@5.78.0)
      babel-plugin-styled-components:
        specifier: ^2.1.1
        version: 2.1.1(styled-components@5.3.9)
      babel-plugin-transform-commonjs:
        specifier: ^1.1.6
        version: 1.1.6(@babel/core@7.21.4)
      chalk:
        specifier: 4.1.2
        version: 4.1.2
      circular-dependency-plugin:
        specifier: ^5.2.2
        version: 5.2.2(webpack@5.78.0)
      constructs:
        specifier: ^10.2.69
        version: 10.2.69
      copy-webpack-plugin:
        specifier: ^11.0.0
        version: 11.0.0(webpack@5.78.0)
      css-loader:
        specifier: ^6.7.3
        version: 6.7.3(webpack@5.78.0)
      css-minimizer-webpack-plugin:
        specifier: ^4.2.2
        version: 4.2.2(webpack@5.78.0)
      eslint:
        specifier: ^7.32.0
        version: 7.32.0
      eslint-config-prettier:
        specifier: ^8.8.0
        version: 8.8.0(eslint@7.32.0)
      eslint-plugin-prettier:
        specifier: ^4.2.1
        version: 4.2.1(eslint-config-prettier@8.8.0)(eslint@7.32.0)(prettier@3.2.5)
      eslint-plugin-react:
        specifier: ^7.32.2
        version: 7.32.2(eslint@7.32.0)
      eslint-webpack-plugin:
        specifier: ^4.0.0
        version: 4.0.0(eslint@7.32.0)(webpack@5.78.0)
      express:
        specifier: ^4.18.2
        version: 4.18.2
      file-loader:
        specifier: ^6.2.0
        version: 6.2.0(webpack@5.78.0)
      fork-ts-checker-webpack-plugin:
        specifier: ^7.3.0
        version: 7.3.0(typescript@5.0.3)(webpack@5.78.0)
      friendly-errors-webpack-plugin:
        specifier: ^1.7.0
        version: 1.7.0(webpack@5.78.0)
      html-webpack-plugin:
        specifier: ^5.5.0
        version: 5.5.0(webpack@5.78.0)
      ignore-loader:
        specifier: ^0.1.2
        version: 0.1.2
      json-server:
        specifier: ^0.17.3
        version: 0.17.3
      koa-mount:
        specifier: ^4.0.0
        version: 4.0.0
      koa-static:
        specifier: ^5.0.0
        version: 5.0.0
      less:
        specifier: ^4.1.3
        version: 4.1.3
      less-loader:
        specifier: ^11.1.0
        version: 11.1.0(less@4.1.3)(webpack@5.78.0)
      lodash:
        specifier: ^4.17.21
        version: 4.17.21
      mini-css-extract-plugin:
        specifier: ^2.7.5
        version: 2.7.5(webpack@5.78.0)
      minimist:
        specifier: ^1.2.8
        version: 1.2.8
      nodemon:
        specifier: ^3.1.0
        version: 3.1.0
      null-loader:
        specifier: ^4.0.1
        version: 4.0.1(webpack@5.78.0)
      pm2:
        specifier: ^5.3.0
        version: 5.3.0
      postcss-loader:
        specifier: ^7.2.4
        version: 7.2.4(@types/node@18.15.11)(postcss@8.4.38)(ts-node@10.9.2)(typescript@5.0.3)(webpack@5.78.0)
      postcss-preset-env:
        specifier: ^7.8.3
        version: 7.8.3(postcss@8.4.38)
      react-refresh:
        specifier: ^0.14.0
        version: 0.14.0
      simple-progress-webpack-plugin:
        specifier: ^1.1.2
        version: 1.1.2(webpack@5.78.0)
      style-resources-loader:
        specifier: ^1.5.0
        version: 1.5.0(webpack@5.78.0)
      tailwindcss:
        specifier: ^3.3.1
        version: 3.3.1(postcss@8.4.38)(ts-node@10.9.2)
      terser-webpack-plugin:
        specifier: ^5.3.7
        version: 5.3.7(webpack@5.78.0)
      thread-loader:
        specifier: ^3.0.4
        version: 3.0.4(webpack@5.78.0)
      tsconfig-paths-webpack-plugin:
        specifier: ^4.0.1
        version: 4.0.1
      typescript:
        specifier: ^5.0.3
        version: 5.0.3
      webpack:
        specifier: ^5.78.0
        version: 5.78.0(webpack-cli@5.0.1)
      webpack-build-notifier:
        specifier: ^2.3.0
        version: 2.3.0
      webpack-bundle-analyzer:
        specifier: ^4.8.0
        version: 4.8.0
      webpack-cli:
        specifier: ^5.0.1
        version: 5.0.1(webpack-bundle-analyzer@4.8.0)(webpack-dev-server@4.13.2)(webpack@5.78.0)
      webpack-dev-middleware:
        specifier: ^6.0.2
        version: 6.0.2(webpack@5.78.0)
      webpack-dev-server:
        specifier: ^4.13.2
        version: 4.13.2(webpack-cli@5.0.1)(webpack@5.78.0)
      webpack-hot-middleware:
        specifier: ^2.25.3
        version: 2.25.3
      webpack-manifest-plugin:
        specifier: ^5.0.0
        version: 5.0.0(webpack@5.78.0)
      webpack-merge:
        specifier: ^5.8.0
        version: 5.8.0
      webpack-node-externals:
        specifier: ^3.0.0
        version: 3.0.0
      webpackbar:
        specifier: ^5.0.2
        version: 5.0.2(webpack@5.78.0)

packages:

  '@ampproject/remapping@2.2.1':
    resolution: {integrity: sha512-lFMjJTrFL3j7L9yBxwYfCq2k6qqwHyzuUl/XBnif78PWTJYyL/dfowQHWE3sp6U6ZzqWiiIZnpTMO96zhkjwtg==}
    engines: {node: '>=6.0.0'}

  '@aws-cdk/asset-awscli-v1@2.2.200':
    resolution: {integrity: sha512-Kf5J8DfJK4wZFWT2Myca0lhwke7LwHcHBo+4TvWOGJrFVVKVuuiLCkzPPRBQQVDj0Vtn2NBokZAz8pfMpAqAKg==}

  '@aws-cdk/asset-kubectl-v20@2.1.2':
    resolution: {integrity: sha512-3M2tELJOxQv0apCIiuKQ4pAbncz9GuLwnKFqxifWfe77wuMxyTRPmxssYHs42ePqzap1LT6GDcPygGs+hHstLg==}

  '@aws-cdk/asset-node-proxy-agent-v5@2.0.165':
    resolution: {integrity: sha512-bsyLQD/vqXQcc9RDmlM1XqiFNO/yewgVFXmkMcQkndJbmE/jgYkzewwYGrBlfL725hGLQipXq19+jwWwdsXQqg==}

  '@babel/code-frame@7.12.11':
    resolution: {integrity: sha512-Zt1yodBx1UcyiePMSkWnU4hPqhwq7hGi2nFL1LeA3EUl+q2LQx16MISgJ0+z7dnmgvP9QtIleuETGOiOH1RcIw==}

  '@babel/code-frame@7.21.4':
    resolution: {integrity: sha512-LYvhNKfwWSPpocw8GI7gpK2nq3HSDuEPC/uSYaALSJu9xjsalaaYFOq0Pwt5KmVqwEbZlDu81aLXwBOmD/Fv9g==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.21.4':
    resolution: {integrity: sha512-/DYyDpeCfaVinT40FPGdkkb+lYSKvsVuMjDAG7jPOWWiM1ibOaB9CXJAlc4d1QpP/U2q2P9jbrSlClKSErd55g==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.21.4':
    resolution: {integrity: sha512-qt/YV149Jman/6AfmlxJ04LMIu8bMoyl3RB91yTFrxQmgbrSvQMy7cI8Q62FHx1t8wJ8B5fu0UDoLwHAhUo1QA==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.21.4':
    resolution: {integrity: sha512-NieM3pVIYW2SwGzKoqfPrQsf4xGs9M9AIG3ThppsSRmO+m7eQhmI6amajKMUeIO37wFfsvnvcxQFx6x6iqxDnA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.18.6':
    resolution: {integrity: sha512-duORpUiYrEpzKIop6iNbjnwKLAKnJ47csTyRACyEmWj0QdUrm5aqNJGHSSEQSUAvNW0ojX0dOmK9dZduvkfeXA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-builder-binary-assignment-operator-visitor@7.18.9':
    resolution: {integrity: sha512-yFQ0YCHoIqarl8BCRwBL8ulYUaZpz3bNsA7oFepAzee+8/+ImtADXNOmO5vJvsPff3qi+hvpkY/NYBTrBQgdNw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.21.4':
    resolution: {integrity: sha512-Fa0tTuOXZ1iL8IeDFUWCzjZcn+sJGd9RZdH9esYVjEejGmzf+FFYQpMi/kZUk2kPy/q1H3/GPw7np8qar/stfg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-create-class-features-plugin@7.21.4':
    resolution: {integrity: sha512-46QrX2CQlaFRF4TkwfTt6nJD7IHq8539cCL7SDpqWSDeJKY1xylKKY5F/33mJhLZ3mFvKv2gGrVS6NkyF6qs+Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-create-regexp-features-plugin@7.21.4':
    resolution: {integrity: sha512-M00OuhU+0GyZ5iBBN9czjugzWrEq2vDpf/zCYHxxf93ul/Q5rv+a5h+/+0WnI1AebHNVtl5bFV0qsJoH23DbfA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-define-polyfill-provider@0.3.3':
    resolution: {integrity: sha512-z5aQKU4IzbqCC1XH0nAqfsFLMVSo22SBKUc0BxGrLkolTdPTructy0ToNnlO2zA4j9Q/7pjMZf0DSY+DSTYzww==}
    peerDependencies:
      '@babel/core': ^7.4.0-0

  '@babel/helper-environment-visitor@7.18.9':
    resolution: {integrity: sha512-3r/aACDJ3fhQ/EVgFy0hpj8oHyHpQc+LPtJoY9SzTThAsStm4Ptegq92vqKoE3vD706ZVFWITnMnxucw+S9Ipg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-explode-assignable-expression@7.18.6':
    resolution: {integrity: sha512-eyAYAsQmB80jNfg4baAtLeWAQHfHFiR483rzFK+BhETlGZaQC9bsfrugfXDCbRHLQbIA7U5NxhhOxN7p/dWIcg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-function-name@7.21.0':
    resolution: {integrity: sha512-HfK1aMRanKHpxemaY2gqBmL04iAPOPRj7DxtNbiDOrJK+gdwkiNRVpCpUJYbUT+aZyemKN8brqTOxzCaG6ExRg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-hoist-variables@7.18.6':
    resolution: {integrity: sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-member-expression-to-functions@7.21.0':
    resolution: {integrity: sha512-Muu8cdZwNN6mRRNG6lAYErJ5X3bRevgYR2O8wN0yn7jJSnGDu6eG59RfT29JHxGUovyfrh6Pj0XzmR7drNVL3Q==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.21.4':
    resolution: {integrity: sha512-orajc5T2PsRYUN3ZryCEFeMDYwyw09c/pZeaQEZPH0MpKzSvn3e0uXsDBu3k03VI+9DBiRo+l22BfKTpKwa/Wg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.21.2':
    resolution: {integrity: sha512-79yj2AR4U/Oqq/WOV7Lx6hUjau1Zfo4cI+JLAVYeMV5XIlbOhmjEk5ulbTc9fMpmlojzZHkUUxAiK+UKn+hNQQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-optimise-call-expression@7.18.6':
    resolution: {integrity: sha512-HP59oD9/fEHQkdcbgFCnbmgH5vIQTJbxh2yf+CdM89/glUNnuzr87Q8GIjGEnOktTROemO0Pe0iPAYbqZuOUiA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.20.2':
    resolution: {integrity: sha512-8RvlJG2mj4huQ4pZ+rU9lqKi9ZKiRmuvGuM2HlWmkmgOhbs6zEAw6IEiJ5cQqGbDzGZOhwuOQNtZMi/ENLjZoQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-remap-async-to-generator@7.18.9':
    resolution: {integrity: sha512-dI7q50YKd8BAv3VEfgg7PS7yD3Rtbi2J1XMXaalXO0W0164hYLnh8zpjRS0mte9MfVp/tltvr/cfdXPvJr1opA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-replace-supers@7.20.7':
    resolution: {integrity: sha512-vujDMtB6LVfNW13jhlCrp48QNslK6JXi7lQG736HVbHz/mbf4Dc7tIRh1Xf5C0rF7BP8iiSxGMCmY6Ci1ven3A==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-simple-access@7.20.2':
    resolution: {integrity: sha512-+0woI/WPq59IrqDYbVGfshjT5Dmk/nnbdpcF8SnMhhXObpTq2KNBdLFRFrkVdbDOyUmHBCxzm5FHV1rACIkIbA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-skip-transparent-expression-wrappers@7.20.0':
    resolution: {integrity: sha512-5y1JYeNKfvnT8sZcK9DVRtpTbGiomYIHviSP3OQWmDPU3DeH4a1ZlT/N2lyQ5P8egjcRaT/Y9aNqUxK0WsnIIg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-split-export-declaration@7.18.6':
    resolution: {integrity: sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.19.4':
    resolution: {integrity: sha512-nHtDoQcuqFmwYNYPz3Rah5ph2p8PFeFCsZk9A/48dPc/rGocJ5J3hAAZ7pb76VWX3fZKu+uEr/FhH5jLx7umrw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.19.1':
    resolution: {integrity: sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.21.0':
    resolution: {integrity: sha512-rmL/B8/f0mKS2baE9ZpyTcTavvEuWhTTW8amjzXNvYG4AwBsqTLikfXsEofsJEfKHf+HQVQbFOHy6o+4cnC/fQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-wrap-function@7.20.5':
    resolution: {integrity: sha512-bYMxIWK5mh+TgXGVqAtnu5Yn1un+v8DDZtqyzKRLUzrh70Eal2O3aZ7aPYiMADO4uKlkzOiRiZ6GX5q3qxvW9Q==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.21.0':
    resolution: {integrity: sha512-XXve0CBtOW0pd7MRzzmoyuSj0e3SEzj8pgyFxnTT1NJZL38BD1MK7yYrm8yefRPIDvNNe14xR4FdbHwpInD4rA==}
    engines: {node: '>=6.9.0'}

  '@babel/highlight@7.18.6':
    resolution: {integrity: sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.21.4':
    resolution: {integrity: sha512-alVJj7k7zIxqBZ7BTRhz0IqJFxW1VJbm6N8JbcYhQ186df9ZBPbZBmWSqAMXwHGsCJdYks7z/voa3ibiS5bCIw==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.18.6':
    resolution: {integrity: sha512-Dgxsyg54Fx1d4Nge8UnvTrED63vrwOdPmyvPzlNN/boaliRP54pm3pGzZD1SJUwrBA+Cs/xdG8kXX6Mn/RfISQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.20.7':
    resolution: {integrity: sha512-sbr9+wNE5aXMBBFBICk01tt7sBf2Oc9ikRFEcem/ZORup9IMUdNhW7/wVLEbbtlWOsEubJet46mHAL2C8+2jKQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0

  '@babel/plugin-proposal-async-generator-functions@7.20.7':
    resolution: {integrity: sha512-xMbiLsn/8RK7Wq7VeVytytS2L6qE69bXPB10YCmMdDZbKF4okCqY74pI/jJQ/8U0b/F6NrT2+14b8/P9/3AMGA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-class-properties@7.18.6':
    resolution: {integrity: sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-class-static-block@7.21.0':
    resolution: {integrity: sha512-XP5G9MWNUskFuP30IfFSEFB0Z6HzLIUcjYM4bYOPHXl7eiJ9HFv8tWj6TXTN5QODiEhDZAeI4hLok2iHFFV4hw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0

  '@babel/plugin-proposal-dynamic-import@7.18.6':
    resolution: {integrity: sha512-1auuwmK+Rz13SJj36R+jqFPMJWyKEDd7lLSdOj4oJK0UTgGueSAtkrCvz9ewmgyU/P941Rv2fQwZJN8s6QruXw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-export-namespace-from@7.18.9':
    resolution: {integrity: sha512-k1NtHyOMvlDDFeb9G5PhUXuGj8m/wiwojgQVEhJ/fsVsMCpLyOP4h0uGEjYJKrRI+EVPlb5Jk+Gt9P97lOGwtA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-json-strings@7.18.6':
    resolution: {integrity: sha512-lr1peyn9kOdbYc0xr0OdHTZ5FMqS6Di+H0Fz2I/JwMzGmzJETNeOFq2pBySw6X/KFL5EWDjlJuMsUGRFb8fQgQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-logical-assignment-operators@7.20.7':
    resolution: {integrity: sha512-y7C7cZgpMIjWlKE5T7eJwp+tnRYM89HmRvWM5EQuB5BoHEONjmQ8lSNmBUwOyy/GFRsohJED51YBF79hE1djug==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-nullish-coalescing-operator@7.18.6':
    resolution: {integrity: sha512-wQxQzxYeJqHcfppzBDnm1yAY0jSRkUXR2z8RePZYrKwMKgMlE8+Z6LUno+bd6LvbGh8Gltvy74+9pIYkr+XkKA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-numeric-separator@7.18.6':
    resolution: {integrity: sha512-ozlZFogPqoLm8WBr5Z8UckIoE4YQ5KESVcNudyXOR8uqIkliTEgJ3RoketfG6pmzLdeZF0H/wjE9/cCEitBl7Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-object-rest-spread@7.20.7':
    resolution: {integrity: sha512-d2S98yCiLxDVmBmE8UjGcfPvNEUbA1U5q5WxaWFUGRzJSVAZqm5W6MbPct0jxnegUZ0niLeNX+IOzEs7wYg9Dg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-optional-catch-binding@7.18.6':
    resolution: {integrity: sha512-Q40HEhs9DJQyaZfUjjn6vE8Cv4GmMHCYuMGIWUnlxH6400VGxOuwWsPt4FxXxJkC/5eOzgn0z21M9gMT4MOhbw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-optional-chaining@7.21.0':
    resolution: {integrity: sha512-p4zeefM72gpmEe2fkUr/OnOXpWEf8nAgk7ZYVqqfFiyIG7oFfVZcCrU64hWn5xp4tQ9LkV4bTIa5rD0KANpKNA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-private-methods@7.18.6':
    resolution: {integrity: sha512-nutsvktDItsNn4rpGItSNV2sz1XwS+nfU0Rg8aCx3W3NOKVzdMjJRu0O5OkgDp3ZGICSTbgRpxZoWsxoKRvbeA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-private-property-in-object@7.21.0':
    resolution: {integrity: sha512-ha4zfehbJjc5MmXBlHec1igel5TJXXLDDRbuJ4+XT2TJcyD9/V1919BA8gMvsdHcNMBy4WBUBiRb3nw/EQUtBw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-unicode-property-regex@7.18.6':
    resolution: {integrity: sha512-2BShG/d5yoZyXZfVePH91urL5wTG6ASZU9M4o03lKK8u8UW1y08OMttBSOADTcJrnPMpvDXRG3G8fyLh4ovs8w==}
    engines: {node: '>=4'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-async-generators@7.8.4':
    resolution: {integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-properties@7.12.13':
    resolution: {integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-static-block@7.14.5':
    resolution: {integrity: sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-dynamic-import@7.8.3':
    resolution: {integrity: sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-export-namespace-from@7.8.3':
    resolution: {integrity: sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-assertions@7.20.0':
    resolution: {integrity: sha512-IUh1vakzNoWalR8ch/areW7qFopR2AEw03JlG7BbrDqmQ4X3q9uuipQwSGrUn7oGiemKjtSLDhNtQHzMHr1JdQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-json-strings@7.8.3':
    resolution: {integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-jsx@7.21.4':
    resolution: {integrity: sha512-5hewiLct5OKyh6PLKEYaFclcqtIgCb6bmELouxjF6up5q3Sov7rOayW4RwhbaBL0dit8rA80GNfY+UuDp2mBbQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4':
    resolution: {integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3':
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-numeric-separator@7.10.4':
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-object-rest-spread@7.8.3':
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-catch-binding@7.8.3':
    resolution: {integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-chaining@7.8.3':
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-private-property-in-object@7.14.5':
    resolution: {integrity: sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-top-level-await@7.14.5':
    resolution: {integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-typescript@7.21.4':
    resolution: {integrity: sha512-xz0D39NvhQn4t4RNsHmDnnsaQizIlUkdtYvLs8La1BlfjQ6JEwxkJGeqJMW2tAXx+q6H+WFuUTXNdYVpEya0YA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-arrow-functions@7.20.7':
    resolution: {integrity: sha512-3poA5E7dzDomxj9WXWwuD6A5F3kc7VXwIJO+E+J8qtDtS+pXPAhrgEyh+9GBwBgPq1Z+bB+/JD60lp5jsN7JPQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-to-generator@7.20.7':
    resolution: {integrity: sha512-Uo5gwHPT9vgnSXQxqGtpdufUiWp96gk7yiP4Mp5bm1QMkEmLXBO7PAGYbKoJ6DhAwiNkcHFBol/x5zZZkL/t0Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoped-functions@7.18.6':
    resolution: {integrity: sha512-ExUcOqpPWnliRcPqves5HJcJOvHvIIWfuS4sroBUenPuMdmW+SMHDakmtS7qOo13sVppmUijqeTv7qqGsvURpQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoping@7.21.0':
    resolution: {integrity: sha512-Mdrbunoh9SxwFZapeHVrwFmri16+oYotcZysSzhNIVDwIAb1UV+kvnxULSYq9J3/q5MDG+4X6w8QVgD1zhBXNQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-classes@7.21.0':
    resolution: {integrity: sha512-RZhbYTCEUAe6ntPehC4hlslPWosNHDox+vAs4On/mCLRLfoDVHf6hVEd7kuxr1RnHwJmxFfUM3cZiZRmPxJPXQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-computed-properties@7.20.7':
    resolution: {integrity: sha512-Lz7MvBK6DTjElHAmfu6bfANzKcxpyNPeYBGEafyA6E5HtRpjpZwU+u7Qrgz/2OR0z+5TvKYbPdphfSaAcZBrYQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-destructuring@7.21.3':
    resolution: {integrity: sha512-bp6hwMFzuiE4HqYEyoGJ/V2LeIWn+hLVKc4pnj++E5XQptwhtcGmSayM029d/j2X1bPKGTlsyPwAubuU22KhMA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-dotall-regex@7.18.6':
    resolution: {integrity: sha512-6S3jpun1eEbAxq7TdjLotAsl4WpQI9DxfkycRcKrjhQYzU87qpXdknpBg/e+TdcMehqGnLFi7tnFUBR02Vq6wg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-keys@7.18.9':
    resolution: {integrity: sha512-d2bmXCtZXYc59/0SanQKbiWINadaJXqtvIQIzd4+hNwkWBgyCd5F/2t1kXoUdvPMrxzPvhK6EMQRROxsue+mfw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-exponentiation-operator@7.18.6':
    resolution: {integrity: sha512-wzEtc0+2c88FVR34aQmiz56dxEkxr2g8DQb/KfaFa1JYXOFVsbhvAonFN6PwVWj++fKmku8NP80plJ5Et4wqHw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-for-of@7.21.0':
    resolution: {integrity: sha512-LlUYlydgDkKpIY7mcBWvyPPmMcOphEyYA27Ef4xpbh1IiDNLr0kZsos2nf92vz3IccvJI25QUwp86Eo5s6HmBQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-function-name@7.18.9':
    resolution: {integrity: sha512-WvIBoRPaJQ5yVHzcnJFor7oS5Ls0PYixlTYE63lCj2RtdQEl15M68FXQlxnG6wdraJIXRdR7KI+hQ7q/9QjrCQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-literals@7.18.9':
    resolution: {integrity: sha512-IFQDSRoTPnrAIrI5zoZv73IFeZu2dhu6irxQjY9rNjTT53VmKg9fenjvoiOWOkJ6mm4jKVPtdMzBY98Fp4Z4cg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-member-expression-literals@7.18.6':
    resolution: {integrity: sha512-qSF1ihLGO3q+/g48k85tUjD033C29TNTVB2paCwZPVmOsjn9pClvYYrM2VeJpBY2bcNkuny0YUyTNRyRxJ54KA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-amd@7.20.11':
    resolution: {integrity: sha512-NuzCt5IIYOW0O30UvqktzHYR2ud5bOWbY0yaxWZ6G+aFzOMJvrs5YHNikrbdaT15+KNO31nPOy5Fim3ku6Zb5g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.21.2':
    resolution: {integrity: sha512-Cln+Yy04Gxua7iPdj6nOV96smLGjpElir5YwzF0LBPKoPlLDNJePNlrGGaybAJkd0zKRnOVXOgizSqPYMNYkzA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-systemjs@7.20.11':
    resolution: {integrity: sha512-vVu5g9BPQKSFEmvt2TA4Da5N+QVS66EX21d8uoOihC+OCpUoGvzVsXeqFdtAEfVa5BILAeFt+U7yVmLbQnAJmw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-umd@7.18.6':
    resolution: {integrity: sha512-dcegErExVeXcRqNtkRU/z8WlBLnvD4MRnHgNs3MytRO1Mn1sHRyhbcpYbVMGclAqOjdW+9cfkdZno9dFdfKLfQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-named-capturing-groups-regex@7.20.5':
    resolution: {integrity: sha512-mOW4tTzi5iTLnw+78iEq3gr8Aoq4WNRGpmSlrogqaiCBoR1HFhpU4JkpQFOHfeYx3ReVIFWOQJS4aZBRvuZ6mA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-new-target@7.18.6':
    resolution: {integrity: sha512-DjwFA/9Iu3Z+vrAn+8pBUGcjhxKguSMlsFqeCKbhb9BAV756v0krzVK04CRDi/4aqmk8BsHb4a/gFcaA5joXRw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-super@7.18.6':
    resolution: {integrity: sha512-uvGz6zk+pZoS1aTZrOvrbj6Pp/kK2mp45t2B+bTDre2UgsZZ8EZLSJtUg7m/no0zOJUWgFONpB7Zv9W2tSaFlA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-parameters@7.21.3':
    resolution: {integrity: sha512-Wxc+TvppQG9xWFYatvCGPvZ6+SIUxQ2ZdiBP+PHYMIjnPXD+uThCshaz4NZOnODAtBjjcVQQ/3OKs9LW28purQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-property-literals@7.18.6':
    resolution: {integrity: sha512-cYcs6qlgafTud3PAzrrRNbQtfpQ8+y/+M5tKmksS9+M1ckbH6kzY8MrexEM9mcA6JDsukE19iIRvAyYl463sMg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-constant-elements@7.21.3':
    resolution: {integrity: sha512-4DVcFeWe/yDYBLp0kBmOGFJ6N2UYg7coGid1gdxb4co62dy/xISDMaYBXBVXEDhfgMk7qkbcYiGtwd5Q/hwDDQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-display-name@7.18.6':
    resolution: {integrity: sha512-TV4sQ+T013n61uMoygyMRm+xf04Bd5oqFpv2jAEQwSZ8NwQA7zeRPg1LMVg2PWi3zWBz+CLKD+v5bcpZ/BS0aA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-development@7.18.6':
    resolution: {integrity: sha512-SA6HEjwYFKF7WDjWcMcMGUimmw/nhNRDWxr+KaLSCrkD/LMDBvWRmHAYgE1HDeF8KUuI8OAu+RT6EOtKxSW2qA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx@7.21.0':
    resolution: {integrity: sha512-6OAWljMvQrZjR2DaNhVfRz6dkCAVV+ymcLUmaf8bccGOHn2v5rHJK3tTpij0BuhdYWP4LLaqj5lwcdlpAAPuvg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-pure-annotations@7.18.6':
    resolution: {integrity: sha512-I8VfEPg9r2TRDdvnHgPepTKvuRomzA8+u+nhY7qSI1fR2hRNebasZEETLyM5mAUr0Ku56OkXJ0I7NHJnO6cJiQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regenerator@7.20.5':
    resolution: {integrity: sha512-kW/oO7HPBtntbsahzQ0qSE3tFvkFwnbozz3NWFhLGqH75vLEg+sCGngLlhVkePlCs3Jv0dBBHDzCHxNiFAQKCQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-reserved-words@7.18.6':
    resolution: {integrity: sha512-oX/4MyMoypzHjFrT1CdivfKZ+XvIPMFXwwxHp/r0Ddy2Vuomt4HDFGmft1TAY2yiTKiNSsh3kjBAzcM8kSdsjA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-shorthand-properties@7.18.6':
    resolution: {integrity: sha512-eCLXXJqv8okzg86ywZJbRn19YJHU4XUa55oz2wbHhaQVn/MM+XhukiT7SYqp/7o00dg52Rj51Ny+Ecw4oyoygw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-spread@7.20.7':
    resolution: {integrity: sha512-ewBbHQ+1U/VnH1fxltbJqDeWBU1oNLG8Dj11uIv3xVf7nrQu0bPGe5Rf716r7K5Qz+SqtAOVswoVunoiBtGhxw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-sticky-regex@7.18.6':
    resolution: {integrity: sha512-kfiDrDQ+PBsQDO85yj1icueWMfGfJFKN1KCkndygtu/C9+XUfydLC8Iv5UYJqRwy4zk8EcplRxEOeLyjq1gm6Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-template-literals@7.18.9':
    resolution: {integrity: sha512-S8cOWfT82gTezpYOiVaGHrCbhlHgKhQt8XH5ES46P2XWmX92yisoZywf5km75wv5sYcXDUCLMmMxOLCtthDgMA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typeof-symbol@7.18.9':
    resolution: {integrity: sha512-SRfwTtF11G2aemAZWivL7PD+C9z52v9EvMqH9BuYbabyPuKUvSWks3oCg6041pT925L4zVFqaVBeECwsmlguEw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typescript@7.21.3':
    resolution: {integrity: sha512-RQxPz6Iqt8T0uw/WsJNReuBpWpBqs/n7mNo18sKLoTbMp+UrEekhH+pKSVC7gWz+DNjo9gryfV8YzCiT45RgMw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-escapes@7.18.10':
    resolution: {integrity: sha512-kKAdAI+YzPgGY/ftStBFXTI1LZFju38rYThnfMykS+IXy8BVx+res7s2fxf1l8I35DV2T97ezo6+SGrXz6B3iQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-regex@7.18.6':
    resolution: {integrity: sha512-gE7A6Lt7YLnNOL3Pb9BNeZvi+d8l7tcRrG4+pwJjK9hD2xX4mEvjlQW60G9EEmfXVYRPv9VRQcyegIVHCql/AA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-env@7.21.4':
    resolution: {integrity: sha512-2W57zHs2yDLm6GD5ZpvNn71lZ0B/iypSdIeq25OurDKji6AdzV07qp4s3n1/x5BqtiGaTrPN3nerlSCaC5qNTw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-modules@0.1.5':
    resolution: {integrity: sha512-A57th6YRG7oR3cq/yt/Y84MvGgE0eJG2F1JLhKuyG+jFxEgrd/HAMJatiFtmOiZurz+0DkrvbheCLaV5f2JfjA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-react@7.18.6':
    resolution: {integrity: sha512-zXr6atUmyYdiWRVLOZahakYmOBHtWc2WGCkP8PYTgZi0iJXDY2CN180TdrIW4OGOAdLc7TifzDIvtx6izaRIzg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-typescript@7.18.6':
    resolution: {integrity: sha512-s9ik86kXBAnD760aybBucdpnLsAt0jK1xqJn2juOn9lkOvSHV60os5hxoVJsPzMQxvnUJFAlkont2DvvaYEBtQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/regjsgen@0.8.0':
    resolution: {integrity: sha512-x/rqGMdzj+fWZvCOYForTghzbtqPDZ5gPwaoNGHdgDfF2QA/XZbCBp4Moo5scrkAMPhB7z26XM/AaHuIJdgauA==}

  '@babel/runtime@7.21.0':
    resolution: {integrity: sha512-xwII0//EObnq89Ji5AKYQaRYiW/nZ3llSv29d49IuxPhKbtJoLP+9QUUZ4nVragQVtaVGeZrpB+ZtG/Pdy/POw==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.20.7':
    resolution: {integrity: sha512-8SegXApWe6VoNw0r9JHpSteLKTpTiLZ4rMlGIm9JQ18KiCtyQiAMEazujAHrUS5flrcqYZa75ukev3P6QmUwUw==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.21.4':
    resolution: {integrity: sha512-eyKrRHKdyZxqDm+fV1iqL9UAHMoIg0nDaGqfIOd8rKH17m5snv7Gn4qgjBoFfLz9APvjFU/ICT00NVCv1Epp8Q==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.21.4':
    resolution: {integrity: sha512-rU2oY501qDxE8Pyo7i/Orqma4ziCOrby0/9mvbDUGEfvZjb279Nk9k19e2fiCxHbRRpY2ZyrgW1eq22mvmOIzA==}
    engines: {node: '>=6.9.0'}

  '@balena/dockerignore@1.0.2':
    resolution: {integrity: sha512-wMue2Sy4GAVTk6Ic4tJVcnfdau+gx2EnG7S+uAEe+TWJFqE4YoWN4/H8MSLj4eYJKxGg26lZwboEniNiNwZQ6Q==}

  '@cspotcode/source-map-support@0.8.1':
    resolution: {integrity: sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==}
    engines: {node: '>=12'}

  '@csstools/postcss-cascade-layers@1.1.1':
    resolution: {integrity: sha512-+KdYrpKC5TgomQr2DlZF4lDEpHcoxnj5IGddYYfBWJAKfj1JtuHUIqMa+E1pJJ+z3kvDViWMqyqPlG4Ja7amQA==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-color-function@1.1.1':
    resolution: {integrity: sha512-Bc0f62WmHdtRDjf5f3e2STwRAl89N2CLb+9iAwzrv4L2hncrbDwnQD9PCq0gtAt7pOI2leIV08HIBUd4jxD8cw==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-font-format-keywords@1.0.1':
    resolution: {integrity: sha512-ZgrlzuUAjXIOc2JueK0X5sZDjCtgimVp/O5CEqTcs5ShWBa6smhWYbS0x5cVc/+rycTDbjjzoP0KTDnUneZGOg==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-hwb-function@1.0.2':
    resolution: {integrity: sha512-YHdEru4o3Rsbjmu6vHy4UKOXZD+Rn2zmkAmLRfPet6+Jz4Ojw8cbWxe1n42VaXQhD3CQUXXTooIy8OkVbUcL+w==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-ic-unit@1.0.1':
    resolution: {integrity: sha512-Ot1rcwRAaRHNKC9tAqoqNZhjdYBzKk1POgWfhN4uCOE47ebGcLRqXjKkApVDpjifL6u2/55ekkpnFcp+s/OZUw==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-is-pseudo-class@2.0.7':
    resolution: {integrity: sha512-7JPeVVZHd+jxYdULl87lvjgvWldYu+Bc62s9vD/ED6/QTGjy0jy0US/f6BG53sVMTBJ1lzKZFpYmofBN9eaRiA==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-nested-calc@1.0.0':
    resolution: {integrity: sha512-JCsQsw1wjYwv1bJmgjKSoZNvf7R6+wuHDAbi5f/7MbFhl2d/+v+TvBTU4BJH3G1X1H87dHl0mh6TfYogbT/dJQ==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-normalize-display-values@1.0.1':
    resolution: {integrity: sha512-jcOanIbv55OFKQ3sYeFD/T0Ti7AMXc9nM1hZWu8m/2722gOTxFg7xYu4RDLJLeZmPUVQlGzo4jhzvTUq3x4ZUw==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-oklab-function@1.1.1':
    resolution: {integrity: sha512-nJpJgsdA3dA9y5pgyb/UfEzE7W5Ka7u0CX0/HIMVBNWzWemdcTH3XwANECU6anWv/ao4vVNLTMxhiPNZsTK6iA==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-progressive-custom-properties@1.3.0':
    resolution: {integrity: sha512-ASA9W1aIy5ygskZYuWams4BzafD12ULvSypmaLJT2jvQ8G0M3I8PRQhC0h7mG0Z3LI05+agZjqSR9+K9yaQQjA==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.3

  '@csstools/postcss-stepped-value-functions@1.0.1':
    resolution: {integrity: sha512-dz0LNoo3ijpTOQqEJLY8nyaapl6umbmDcgj4AD0lgVQ572b2eqA1iGZYTTWhrcrHztWDDRAX2DGYyw2VBjvCvQ==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-text-decoration-shorthand@1.0.0':
    resolution: {integrity: sha512-c1XwKJ2eMIWrzQenN0XbcfzckOLLJiczqy+YvfGmzoVXd7pT9FfObiSEfzs84bpE/VqfpEuAZ9tCRbZkZxxbdw==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-trigonometric-functions@1.0.2':
    resolution: {integrity: sha512-woKaLO///4bb+zZC2s80l+7cm07M7268MsyG3M0ActXXEFi6SuhvriQYcb58iiKGbjwwIU7n45iRLEHypB47Og==}
    engines: {node: ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-unset-value@1.0.2':
    resolution: {integrity: sha512-c8J4roPBILnelAsdLr4XOAR/GsTm0GJi4XpcfvoWk3U6KiTCqiFYc63KhRMQQX35jYMp4Ao8Ij9+IZRgMfJp1g==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/selector-specificity@2.2.0':
    resolution: {integrity: sha512-+OJ9konv95ClSTOJCmMZqpd5+YGsB2S+x6w3E1oaM8UuR5j8nTNHYSz8c9BEPGDOCMQYIEEGlVPj/VY64iTbGw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss-selector-parser: ^6.0.10

  '@discoveryjs/json-ext@0.5.7':
    resolution: {integrity: sha512-dBVuXR082gk3jsFp7Rd/JI4kytwGHecnCoTtXFb7DB6CNHp4rg5k1bhg0nWdLGLnOV71lmDzGQaLMy8iPLY0pw==}
    engines: {node: '>=10.0.0'}

  '@emotion/is-prop-valid@1.2.0':
    resolution: {integrity: sha512-3aDpDprjM0AwaxGE09bOPkNxHpBd+kA6jty3RnaEXdweX1DF1U3VQpPYb0g1IStAuK7SVQ1cy+bNBBKp4W3Fjg==}

  '@emotion/memoize@0.8.0':
    resolution: {integrity: sha512-G/YwXTkv7Den9mXDO7AhLWkE3q+I92B+VqAE+dYG4NGPaHZGvt3G8Q0p9vmE+sq7rTGphUbAvmQ9YpbfMQGGlA==}

  '@emotion/stylis@0.8.5':
    resolution: {integrity: sha512-h6KtPihKFn3T9fuIrwvXXUOwlx3rfUvfZIcP5a6rh8Y7zjE3O06hT5Ss4S/YI1AYhuZ1kjaE/5EaOOI2NqSylQ==}

  '@emotion/unitless@0.7.5':
    resolution: {integrity: sha512-OWORNpfjMsSSUBVrRBVGECkhWcULOAJz9ZW8uK9qgxD+87M7jHRcvh/A96XXNhXTLmKcoYSQtBEX7lHMO7YRwg==}

  '@eslint-community/eslint-utils@4.4.0':
    resolution: {integrity: sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.5.0':
    resolution: {integrity: sha512-vITaYzIcNmjn5tF5uxcZ/ft7/RXGrMUIS9HalWckEOF6ESiwXKoMzAQf2UW0aVd6rnOeExTJVd5hmWXucBKGXQ==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/eslintrc@0.4.3':
    resolution: {integrity: sha512-J6KFFz5QCYUJq3pf0mjEcCJVERbzv71PUIDczuh9JkwGEzced6CO5ADLHB1rbf/+oPBtoPfMYNOpGDzCANlbXw==}
    engines: {node: ^10.12.0 || >=12.0.0}

  '@eslint/eslintrc@2.0.2':
    resolution: {integrity: sha512-3W4f5tDUra+pA+FzgugqL2pRimUTDJWKr7BINqOpkZrC0uYI0NIc0/JFgBROCU07HR6GieA5m3/rsPIhDmCXTQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@eslint/js@8.38.0':
    resolution: {integrity: sha512-IoD2MfUnOV58ghIHCiil01PcohxjbYR/qCxsoC+xNgUwh1EY8jOOrYmu3d3a71+tJJ23uscEV4X2HJWMsPJu4g==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@formatjs/ecma402-abstract@1.14.3':
    resolution: {integrity: sha512-SlsbRC/RX+/zg4AApWIFNDdkLtFbkq3LNoZWXZCE/nHVKqoIJyaoQyge/I0Y38vLxowUn9KTtXgusLD91+orbg==}

  '@formatjs/fast-memoize@2.0.1':
    resolution: {integrity: sha512-M2GgV+qJn5WJQAYewz7q2Cdl6fobQa69S1AzSM2y0P68ZDbK5cWrJIcPCO395Of1ksftGZoOt4LYCO/j9BKBSA==}

  '@formatjs/icu-messageformat-parser@2.3.1':
    resolution: {integrity: sha512-knF2AkAKN4Upv4oIiKY4Wd/dLH68TNMPgV/tJMu/T6FP9aQwbv8fpj7U3lkyniPaNVxvia56Gxax8MKOjtxLSQ==}

  '@formatjs/icu-skeleton-parser@1.3.18':
    resolution: {integrity: sha512-ND1ZkZfmLPcHjAH1sVpkpQxA+QYfOX3py3SjKWMUVGDow18gZ0WPqz3F+pJLYQMpS2LnnQ5zYR2jPVYTbRwMpg==}

  '@formatjs/intl-displaynames@6.3.0':
    resolution: {integrity: sha512-bwv4oWxz5/2yGVo1MbfGtUmSkehTKVWrBUCP+7NfpLy3vZD5SqA0ZY/M5DMJw77c34XdI0p/uguT7drnFamKRA==}

  '@formatjs/intl-listformat@7.2.0':
    resolution: {integrity: sha512-fk3UhVTL30kzWHdgAwZXUx4PzfXdaNcBkvhe9u9frzmdkc3srrwAkwXFCo60oaT6rL0BZbbTcGQ8Z3WhBQh0tQ==}

  '@formatjs/intl-localematcher@0.2.32':
    resolution: {integrity: sha512-k/MEBstff4sttohyEpXxCmC3MqbUn9VvHGlZ8fauLzkbwXmVrEeyzS+4uhrvAk9DWU9/7otYWxyDox4nT/KVLQ==}

  '@formatjs/intl@2.7.0':
    resolution: {integrity: sha512-4riPmivUowNd1Jwz9LSB+OZ8iygnjzgqJD30VdwH43ry15CvRktejWQwgH28rkSFc60DkNdBnuMyf6NpsqJnzA==}
    peerDependencies:
      typescript: ^4.7 || 5
    peerDependenciesMeta:
      typescript:
        optional: true

  '@humanwhocodes/config-array@0.11.8':
    resolution: {integrity: sha512-UybHIJzJnR5Qc/MsD9Kr+RpO2h+/P1GhOwdiLPXK5TWk5sgTdu88bTD9UP+CKbPPh5Rni1u0GjAdYQLemG8g+g==}
    engines: {node: '>=10.10.0'}

  '@humanwhocodes/config-array@0.5.0':
    resolution: {integrity: sha512-FagtKFz74XrTl7y6HCzQpwDfXP0yhxe9lHLD1UZxjvZIcbyRz8zTFF/yYNfSfzU414eDwZ1SrO0Qvtyf+wFMQg==}
    engines: {node: '>=10.10.0'}

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    engines: {node: '>=12.22'}

  '@humanwhocodes/object-schema@1.2.1':
    resolution: {integrity: sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==}

  '@jest/schemas@29.4.3':
    resolution: {integrity: sha512-VLYKXQmtmuEz6IxJsrZwzG9NvtkQsWNnWMsKxqWNu3+CnfzJQhp0WDDKWLVV9hLKr0l3SLLFRqcYHjhtyuDVxg==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/types@29.5.0':
    resolution: {integrity: sha512-qbu7kN6czmVRc3xWFQcAN03RAUamgppVUdXrvl1Wr3jlNF93o9mJbGcDWrwGB6ht44u7efB1qCFgVQmca24Uog==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jridgewell/gen-mapping@0.3.3':
    resolution: {integrity: sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.0':
    resolution: {integrity: sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.1.2':
    resolution: {integrity: sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/source-map@0.3.3':
    resolution: {integrity: sha512-b+fsZXeLYi9fEULmfBrhxn4IrPlINf8fiNarzTof004v3lFdntdwa9PF7vFJqm3mg7s+ScJMxXaE3Acp1irZcg==}

  '@jridgewell/sourcemap-codec@1.4.14':
    resolution: {integrity: sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==}

  '@jridgewell/sourcemap-codec@1.4.15':
    resolution: {integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==}

  '@jridgewell/trace-mapping@0.3.18':
    resolution: {integrity: sha512-w+niJYzMHdd7USdiH2U6869nqhD2nbfZXND5Yp93qIbEmnDNk7PD48o+YchRVpzMU7M6jVCbenTR7PA1FLQ9pA==}

  '@jridgewell/trace-mapping@0.3.9':
    resolution: {integrity: sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==}

  '@koa/router@12.0.0':
    resolution: {integrity: sha512-cnnxeKHXlt7XARJptflGURdJaO+ITpNkOHmQu7NHmCoRinPbyvFzce/EG/E8Zy81yQ1W9MoSdtklc3nyaDReUw==}
    engines: {node: '>= 12'}

  '@leichtgewicht/ip-codec@2.0.4':
    resolution: {integrity: sha512-Hcv+nVC0kZnQ3tD9GVu5xSMR4VVYOteQIr/hwFPVEvPdlXqgGEuRjiheChHgdM+JyqdgNcmzZOX/tnl0JOiI7A==}

  '@loadable/babel-plugin@5.15.3':
    resolution: {integrity: sha512-kwEsPxCk8vnwbTfbA4lHqT5t0u0czCQTnCcmOaTjxT5lCn7yZCBTBa9D7lHs+MLM2WyPsZlee3Qh0TTkMMi5jg==}
    engines: {node: '>=8'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@loadable/component@5.15.3':
    resolution: {integrity: sha512-VOgYgCABn6+/7aGIpg7m0Ruj34tGetaJzt4bQ345FwEovDQZ+dua+NWLmuJKv8rWZyxOUSfoJkmGnzyDXH2BAQ==}
    engines: {node: '>=8'}
    peerDependencies:
      react: ^16.3.0 || ^17.0.0 || ^18.0.0

  '@loadable/server@5.15.3':
    resolution: {integrity: sha512-Bm/BGe+RlChuHDKNNXpQOi4AJ0cKVuSLI+J8U0Q06zTIfT0S1RLoy85qs5RXm3cLIfefygL8+9bcYFgeWcoM8A==}
    engines: {node: '>=8'}
    peerDependencies:
      '@loadable/component': ^5.0.1
      react: ^16.3.0 || ^17.0.0 || ^18.0.0

  '@loadable/webpack-plugin@5.15.2':
    resolution: {integrity: sha512-+o87jPHn3E8sqW0aBA+qwKuG8JyIfMGdz3zECv0t/JF0KHhxXtzIlTiqzlIYc5ZpFs/vKSQfjzGIR5tPJjoXDw==}
    engines: {node: '>=8'}
    peerDependencies:
      webpack: '>=4.6.0'

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@opencensus/core@0.0.8':
    resolution: {integrity: sha512-yUFT59SFhGMYQgX0PhoTR0LBff2BEhPrD9io1jWfF/VDbakRfs6Pq60rjv0Z7iaTav5gQlttJCX2+VPxFWCuoQ==}
    engines: {node: '>=6.0'}

  '@opencensus/core@0.0.9':
    resolution: {integrity: sha512-31Q4VWtbzXpVUd2m9JS6HEaPjlKvNMOiF7lWKNmXF84yUcgfAFL5re7/hjDmdyQbOp32oGc+RFV78jXIldVz6Q==}
    engines: {node: '>=6.0'}

  '@opencensus/propagation-b3@0.0.8':
    resolution: {integrity: sha512-PffXX2AL8Sh0VHQ52jJC4u3T0H6wDK6N/4bg7xh4ngMYOIi13aR1kzVvX1sVDBgfGwDOkMbl4c54Xm3tlPx/+A==}
    engines: {node: '>=6.0'}

  '@pm2/agent@2.0.1':
    resolution: {integrity: sha512-QKHMm6yexcvdDfcNE7PL9D6uEjoQPGRi+8dh+rc4Hwtbpsbh5IAvZbz3BVGjcd4HaX6pt2xGpOohG7/Y2L4QLw==}

  '@pm2/io@5.0.0':
    resolution: {integrity: sha512-3rToDVJaRoob5Lq8+7Q2TZFruoEkdORxwzFpZaqF4bmH6Bkd7kAbdPrI/z8X6k1Meq5rTtScM7MmDgppH6aLlw==}
    engines: {node: '>=6.0'}

  '@pm2/js-api@0.6.7':
    resolution: {integrity: sha512-jiJUhbdsK+5C4zhPZNnyA3wRI01dEc6a2GhcQ9qI38DyIk+S+C8iC3fGjcjUbt/viLYKPjlAaE+hcT2/JMQPXw==}
    engines: {node: '>=4.0'}

  '@pm2/pm2-version-check@1.0.4':
    resolution: {integrity: sha512-SXsM27SGH3yTWKc2fKR4SYNxsmnvuBQ9dd6QHtEWmiZ/VqaOYPAIlS8+vMcn27YLtAEBGvNRSh3TPNvtjZgfqA==}

  '@pmmmwh/react-refresh-webpack-plugin@0.5.10':
    resolution: {integrity: sha512-j0Ya0hCFZPd4x40qLzbhGsh9TMtdb+CJQiso+WxLOPNasohq9cc5SNUcwsZaRH6++Xh91Xkm/xHCkuIiIu0LUA==}
    engines: {node: '>= 10.13'}
    peerDependencies:
      '@types/webpack': 4.x || 5.x
      react-refresh: '>=0.10.0 <1.0.0'
      sockjs-client: ^1.4.0
      type-fest: '>=0.17.0 <4.0.0'
      webpack: '>=4.43.0 <6.0.0'
      webpack-dev-server: 3.x || 4.x
      webpack-hot-middleware: 2.x
      webpack-plugin-serve: 0.x || 1.x
    peerDependenciesMeta:
      '@types/webpack':
        optional: true
      sockjs-client:
        optional: true
      type-fest:
        optional: true
      webpack-dev-server:
        optional: true
      webpack-hot-middleware:
        optional: true
      webpack-plugin-serve:
        optional: true

  '@polka/url@1.0.0-next.21':
    resolution: {integrity: sha512-a5Sab1C4/icpTZVzZc5Ghpz88yQtGOyNqYXcZgOssB2uuAr+wF/MvN6bgtW32q7HHrvBki+BsZ0OuNv6EV3K9g==}

  '@principalstudio/html-webpack-inject-preload@1.2.7':
    resolution: {integrity: sha512-KJKkiKG63ugBjf8U0e9jUcI9CLPTFIsxXplEDE0oi3mPpxd90X9SJovo3W2l7yh/ARKIYXhQq8fSXUN7M29TzQ==}
    engines: {node: '>=10.23'}
    peerDependencies:
      html-webpack-plugin: ^4.0.0 || ^5.0.0
      webpack: ^4.0.0 || ^5.0.0

  '@remix-run/router@1.5.0':
    resolution: {integrity: sha512-bkUDCp8o1MvFO+qxkODcbhSqRa6P2GXgrGZVpt0dCXNW2HCSCqYI0ZoAqEOSAjRWmmlKcYgFvN4B4S+zo/f8kg==}
    engines: {node: '>=14'}

  '@sinclair/typebox@0.25.24':
    resolution: {integrity: sha512-XJfwUVUKDHF5ugKwIcxEgc9k8b7HbznCp6eUfWgu710hMPNIO4aw4/zB5RogDQz8nd6gyCDpU9O/m6qYEWY6yQ==}

  '@svgr/babel-plugin-add-jsx-attribute@6.5.1':
    resolution: {integrity: sha512-9PYGcXrAxitycIjRmZB+Q0JaN07GZIWaTBIGQzfaZv+qr1n8X1XUEJ5rZ/vx6OVD9RRYlrNnXWExQXcmZeD/BQ==}
    engines: {node: '>=10'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-remove-jsx-attribute@7.0.0':
    resolution: {integrity: sha512-iiZaIvb3H/c7d3TH2HBeK91uI2rMhZNwnsIrvd7ZwGLkFw6mmunOCoVnjdYua662MqGFxlN9xTq4fv9hgR4VXQ==}
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-remove-jsx-empty-expression@7.0.0':
    resolution: {integrity: sha512-sQQmyo+qegBx8DfFc04PFmIO1FP1MHI1/QEpzcIcclo5OAISsOJPW76ZIs0bDyO/DBSJEa/tDa1W26pVtt0FRw==}
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-replace-jsx-attribute-value@6.5.1':
    resolution: {integrity: sha512-8DPaVVE3fd5JKuIC29dqyMB54sA6mfgki2H2+swh+zNJoynC8pMPzOkidqHOSc6Wj032fhl8Z0TVn1GiPpAiJg==}
    engines: {node: '>=10'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-svg-dynamic-title@6.5.1':
    resolution: {integrity: sha512-FwOEi0Il72iAzlkaHrlemVurgSQRDFbk0OC8dSvD5fSBPHltNh7JtLsxmZUhjYBZo2PpcU/RJvvi6Q0l7O7ogw==}
    engines: {node: '>=10'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-svg-em-dimensions@6.5.1':
    resolution: {integrity: sha512-gWGsiwjb4tw+ITOJ86ndY/DZZ6cuXMNE/SjcDRg+HLuCmwpcjOktwRF9WgAiycTqJD/QXqL2f8IzE2Rzh7aVXA==}
    engines: {node: '>=10'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-transform-react-native-svg@6.5.1':
    resolution: {integrity: sha512-2jT3nTayyYP7kI6aGutkyfJ7UMGtuguD72OjeGLwVNyfPRBD8zQthlvL+fAbAKk5n9ZNcvFkp/b1lZ7VsYqVJg==}
    engines: {node: '>=10'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-transform-svg-component@6.5.1':
    resolution: {integrity: sha512-a1p6LF5Jt33O3rZoVRBqdxL350oge54iZWHNI6LJB5tQ7EelvD/Mb1mfBiZNAan0dt4i3VArkFRjA4iObuNykQ==}
    engines: {node: '>=12'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-preset@6.5.1':
    resolution: {integrity: sha512-6127fvO/FF2oi5EzSQOAjo1LE3OtNVh11R+/8FXa+mHx1ptAaS4cknIjnUA7e6j6fwGGJ17NzaTJFUwOV2zwCw==}
    engines: {node: '>=10'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/core@6.5.1':
    resolution: {integrity: sha512-/xdLSWxK5QkqG524ONSjvg3V/FkNyCv538OIBdQqPNaAta3AsXj/Bd2FbvR87yMbXO2hFSWiAe/Q6IkVPDw+mw==}
    engines: {node: '>=10'}

  '@svgr/hast-util-to-babel-ast@6.5.1':
    resolution: {integrity: sha512-1hnUxxjd83EAxbL4a0JDJoD3Dao3hmjvyvyEV8PzWmLK3B9m9NPlW7GKjFyoWE8nM7HnXzPcmmSyOW8yOddSXw==}
    engines: {node: '>=10'}

  '@svgr/plugin-jsx@6.5.1':
    resolution: {integrity: sha512-+UdQxI3jgtSjCykNSlEMuy1jSRQlGC7pqBCPvkG/2dATdWo082zHTTK3uhnAju2/6XpE6B5mZ3z4Z8Ns01S8Gw==}
    engines: {node: '>=10'}
    peerDependencies:
      '@svgr/core': ^6.0.0

  '@svgr/plugin-svgo@6.5.1':
    resolution: {integrity: sha512-omvZKf8ixP9z6GWgwbtmP9qQMPX4ODXi+wzbVZgomNFsUIlHA1sf4fThdwTWSsZGgvGAG6yE+b/F5gWUkcZ/iQ==}
    engines: {node: '>=10'}
    peerDependencies:
      '@svgr/core': '*'

  '@svgr/webpack@6.5.1':
    resolution: {integrity: sha512-cQ/AsnBkXPkEK8cLbv4Dm7JGXq2XrumKnL1dRpJD9rIO2fTIlJI9a1uCciYG1F2aUsox/hJQyNGbt3soDxSRkA==}
    engines: {node: '>=10'}

  '@tanstack/query-core@4.29.1':
    resolution: {integrity: sha512-vkPewLEG8ua0efo3SsVT0BcBtkq5RZX8oPhDAyKL+k/rdOYSQTEocfGEXSaBwIwsXeOGBUpfKqI+UmHvNqdWXg==}

  '@tanstack/react-query@4.29.3':
    resolution: {integrity: sha512-FPQrMu7PbCgBcVzoRJm7WmQnAFv+LUgZM9KBZ7Vk/+yERH2BDLvQRuAgczQd5Tb1s3HbOktECRDaOkUxdyBAjw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0
      react-native: '*'
    peerDependenciesMeta:
      react-dom:
        optional: true
      react-native:
        optional: true

  '@tootallnate/once@1.1.2':
    resolution: {integrity: sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw==}
    engines: {node: '>= 6'}

  '@trysound/sax@0.2.0':
    resolution: {integrity: sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA==}
    engines: {node: '>=10.13.0'}

  '@tsconfig/node10@1.0.11':
    resolution: {integrity: sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==}

  '@tsconfig/node12@1.0.11':
    resolution: {integrity: sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==}

  '@tsconfig/node14@1.0.3':
    resolution: {integrity: sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==}

  '@tsconfig/node16@1.0.4':
    resolution: {integrity: sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==}

  '@types/accepts@1.3.5':
    resolution: {integrity: sha512-jOdnI/3qTpHABjM5cx1Hc0sKsPoYCp+DP/GJRGtDlPd7fiV9oXGGIcjW/ZOxLIvjGz8MA+uMZI9metHlgqbgwQ==}

  '@types/aws-lambda@8.10.119':
    resolution: {integrity: sha512-Vqm22aZrCvCd6I5g1SvpW151jfqwTzEZ7XJ3yZ6xaZG31nUEOEyzzVImjRcsN8Wi/QyPxId/x8GTtgIbsy8kEw==}

  '@types/body-parser@1.19.2':
    resolution: {integrity: sha512-ALYone6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g==}

  '@types/bonjour@3.5.10':
    resolution: {integrity: sha512-p7ienRMiS41Nu2/igbJxxLDWrSZ0WxM8UQgCeO9KhoVF7cOVFkrKsiDr1EsJIla8vV3oEEjGcz11jc5yimhzZw==}

  '@types/connect-history-api-fallback@1.3.5':
    resolution: {integrity: sha512-h8QJa8xSb1WD4fpKBDcATDNGXghFj6/3GRWG6dhmRcu0RX1Ubasur2Uvx5aeEwlf0MwblEC2bMzzMQntxnw/Cw==}

  '@types/connect@3.4.35':
    resolution: {integrity: sha512-cdeYyv4KWoEgpBISTxWvqYsVy444DOqehiF3fM3ne10AmJ62RSyNkUnxMJXHQWRQQX2eR94m5y1IZyDwBjV9FQ==}

  '@types/content-disposition@0.5.5':
    resolution: {integrity: sha512-v6LCdKfK6BwcqMo+wYW05rLS12S0ZO0Fl4w1h4aaZMD7bqT3gVUns6FvLJKGZHQmYn3SX55JWGpziwJRwVgutA==}

  '@types/cookies@0.7.7':
    resolution: {integrity: sha512-h7BcvPUogWbKCzBR2lY4oqaZbO3jXZksexYJVFvkrFeLgbZjQkU4x8pRq6eg2MHXQhY0McQdqmmsxRWlVAHooA==}

  '@types/eslint-scope@3.7.4':
    resolution: {integrity: sha512-9K4zoImiZc3HlIp6AVUDE4CWYx22a+lhSZMYNpbjW04+YF0KWj4pJXnEMjdnFTiQibFFmElcsasJXDbdI/EPhA==}

  '@types/eslint@8.37.0':
    resolution: {integrity: sha512-Piet7dG2JBuDIfohBngQ3rCt7MgO9xCO4xIMKxBThCq5PNRB91IjlJ10eJVwfoNtvTErmxLzwBZ7rHZtbOMmFQ==}

  '@types/estree@0.0.51':
    resolution: {integrity: sha512-CuPgU6f3eT/XgKKPqKd/gLZV1Xmvf1a2R5POBOGQa6uv82xpls89HU5zKeVoyR8XzHd1RGNOlQlvUe3CFkjWNQ==}

  '@types/estree@1.0.0':
    resolution: {integrity: sha512-WulqXMDUTYAXCjZnk6JtIHPigp55cVtDgDrO2gHRwhyJto21+1zbVCtOYB2L1F9w4qCQ0rOGWBnBe0FNTiEJIQ==}

  '@types/express-serve-static-core@4.17.33':
    resolution: {integrity: sha512-TPBqmR/HRYI3eC2E5hmiivIzv+bidAfXofM+sbonAGvyDhySGw9/PQZFt2BLOrjUUR++4eJVpx6KnLQK1Fk9tA==}

  '@types/express@4.17.17':
    resolution: {integrity: sha512-Q4FmmuLGBG58btUnfS1c1r/NQdlp3DMfGDGig8WhfpA2YRUtEkxAjkZb0yvplJGYdF1fsQ81iMDcH24sSCNC/Q==}

  '@types/hoist-non-react-statics@3.3.1':
    resolution: {integrity: sha512-iMIqiko6ooLrTh1joXodJK5X9xeEALT1kM5G3ZLhD3hszxBdIEd5C75U834D9mLcINgD4OyZf5uQXjkuYydWvA==}

  '@types/html-minifier-terser@6.1.0':
    resolution: {integrity: sha512-oh/6byDPnL1zeNXFrDXFLyZjkr1MsBG667IM792caf1L2UPOOMf65NFzjUH/ltyfwjAGfs1rsX1eftK0jC/KIg==}

  '@types/http-assert@1.5.3':
    resolution: {integrity: sha512-FyAOrDuQmBi8/or3ns4rwPno7/9tJTijVW6aQQjK02+kOQ8zmoNg2XJtAuQhvQcy1ASJq38wirX5//9J1EqoUA==}

  '@types/http-errors@2.0.1':
    resolution: {integrity: sha512-/K3ds8TRAfBvi5vfjuz8y6+GiAYBZ0x4tXv1Av6CWBWn0IlADc+ZX9pMq7oU0fNQPnBwIZl3rmeLp6SBApbxSQ==}

  '@types/http-proxy@1.17.10':
    resolution: {integrity: sha512-Qs5aULi+zV1bwKAg5z1PWnDXWmsn+LxIvUGv6E2+OOMYhclZMO+OXd9pYVf2gLykf2I7IV2u7oTHwChPNsvJ7g==}

  '@types/istanbul-lib-coverage@2.0.4':
    resolution: {integrity: sha512-z/QT1XN4K4KYuslS23k62yDIDLwLFkzxOuMplDtObz0+y7VqJCaO2o+SPwHCvLFZh7xazvvoor2tA/hPz9ee7g==}

  '@types/istanbul-lib-report@3.0.0':
    resolution: {integrity: sha512-plGgXAPfVKFoYfa9NpYDAkseG+g6Jr294RqeqcqDixSbU34MZVJRi/P+7Y8GDpzkEwLaGZZOpKIEmeVZNtKsrg==}

  '@types/istanbul-reports@3.0.1':
    resolution: {integrity: sha512-c3mAZEuK0lvBp8tmuL74XRKn1+y2dcwOUpH7x4WrF6gk1GIgiluDRgMYQtw2OFcBvAJWlt6ASU3tSqxp0Uu0Aw==}

  '@types/json-schema@7.0.11':
    resolution: {integrity: sha512-wOuvG1SN4Us4rez+tylwwwCV1psiNVOkJeM3AUWUNWg/jDQY2+HE/444y5gc+jBmRqASOm2Oeh5c1axHobwRKQ==}

  '@types/json5@0.0.29':
    resolution: {integrity: sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==}

  '@types/keygrip@1.0.2':
    resolution: {integrity: sha512-GJhpTepz2udxGexqos8wgaBx4I/zWIDPh/KOGEwAqtuGDkOUJu5eFvwmdBX4AmB8Odsr+9pHCQqiAqDL/yKMKw==}

  '@types/koa-compose@3.2.5':
    resolution: {integrity: sha512-B8nG/OoE1ORZqCkBVsup/AKcvjdgoHnfi4pZMn5UwAPCbhk/96xyv284eBYW8JlQbQ7zDmnpFr68I/40mFoIBQ==}

  '@types/koa-mount@4.0.2':
    resolution: {integrity: sha512-XnuGwV8bzw22nv2WqOs5a8wCHR2VgSnLLLuBQPzNTmhyiAvH0O6c+994rQVbMaBuwQJKefUInkvKoKuk+21uew==}

  '@types/koa-send@4.1.3':
    resolution: {integrity: sha512-daaTqPZlgjIJycSTNjKpHYuKhXYP30atFc1pBcy6HHqB9+vcymDgYTguPdx9tO4HMOqNyz6bz/zqpxt5eLR+VA==}

  '@types/koa-static@4.0.2':
    resolution: {integrity: sha512-ns/zHg+K6XVPMuohjpOlpkR1WLa4VJ9czgUP9bxkCDn0JZBtUWbD/wKDZzPGDclkQK1bpAEScufCHOy8cbfL0w==}

  '@types/koa@2.13.6':
    resolution: {integrity: sha512-diYUfp/GqfWBAiwxHtYJ/FQYIXhlEhlyaU7lB/bWQrx4Il9lCET5UwpFy3StOAohfsxxvEQ11qIJgT1j2tfBvw==}

  '@types/koa__router@12.0.0':
    resolution: {integrity: sha512-S6eHyZyoWCZLNHyy8j0sMW85cPrpByCbGGU2/BO4IzGiI87aHJ92lZh4E9xfsM9DcbCT469/OIqyC0sSJXSIBQ==}

  '@types/loadable__component@5.13.4':
    resolution: {integrity: sha512-YhoCCxyuvP2XeZNbHbi8Wb9EMaUJuA2VGHxJffcQYrJKIKSkymJrhbzsf9y4zpTmr5pExAAEh5hbF628PAZ8Dg==}

  '@types/loadable__server@5.12.6':
    resolution: {integrity: sha512-LgO5aUQJYFQY5bLuAef4anCZ/SwsEo2hG/D4zfREgUGDXjezBjZCwlUmmpfIQLsR64reLTCijS0m7gsd1k9cKA==}

  '@types/mime@3.0.1':
    resolution: {integrity: sha512-Y4XFY5VJAuw0FgAqPNd6NNoV44jbq9Bz2L7Rh/J6jLTiHBSBJa9fxqQIvkIld4GsoDOcCbvzOUAbLPsSKKg+uA==}

  '@types/node@18.15.11':
    resolution: {integrity: sha512-E5Kwq2n4SbMzQOn6wnmBjuK9ouqlURrcZDVfbo9ftDDTFt3nk7ZKK4GMOzoYgnpQJKcxwQw+lGaBvvlMo0qN/Q==}

  '@types/parse-json@4.0.0':
    resolution: {integrity: sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA==}

  '@types/prop-types@15.7.5':
    resolution: {integrity: sha512-JCB8C6SnDoQf0cNycqd/35A7MjcnK+ZTqE7judS6o7utxUCg6imJg3QK2qzHKszlTjcj2cn+NwMB2i96ubpj7w==}

  '@types/qs@6.9.7':
    resolution: {integrity: sha512-FGa1F62FT09qcrueBA6qYTrJPVDzah9a+493+o2PCXsesWHIn27G98TsSMs3WPNbZIEj4+VJf6saSFpvD+3Zsw==}

  '@types/range-parser@1.2.4':
    resolution: {integrity: sha512-EEhsLsD6UsDM1yFhAvy0Cjr6VwmpMWqFBCb9w07wVugF7w9nfajxLuVmngTIpgS6svCnm6Vaw+MZhoDCKnOfsw==}

  '@types/react-dom@18.3.0':
    resolution: {integrity: sha512-EhwApuTmMBmXuFOikhQLIBUn6uFg81SwLMOAUgodJF14SOBOCMdU04gDoYi0WOJJHD144TL32z4yDqCW3dnkQg==}

  '@types/react@18.3.2':
    resolution: {integrity: sha512-Btgg89dAnqD4vV7R3hlwOxgqobUQKgx3MmrQRi0yYbs/P0ym8XozIAlkqVilPqHQwXs4e9Tf63rrCgl58BcO4w==}

  '@types/retry@0.12.0':
    resolution: {integrity: sha512-wWKOClTTiizcZhXnPY4wikVAwmdYHp8q6DmC+EJUzAMsycb7HB32Kh9RN4+0gExjmPmZSAQjgURXIGATPegAvA==}

  '@types/serve-index@1.9.1':
    resolution: {integrity: sha512-d/Hs3nWDxNL2xAczmOVZNj92YZCS6RGxfBPjKzuu/XirCgXdpKEb88dYNbrYGint6IVWLNP+yonwVAuRC0T2Dg==}

  '@types/serve-static@1.15.1':
    resolution: {integrity: sha512-NUo5XNiAdULrJENtJXZZ3fHtfMolzZwczzBbnAeBbqBwG+LaG6YaJtuwzwGSQZ2wsCrxjEhNNjAkKigy3n8teQ==}

  '@types/sockjs@0.3.33':
    resolution: {integrity: sha512-f0KEEe05NvUnat+boPTZ0dgaLZ4SfSouXUgv5noUiefG2ajgKjmETo9ZJyuqsl7dfl2aHlLJUiki6B4ZYldiiw==}

  '@types/styled-components@5.1.26':
    resolution: {integrity: sha512-KuKJ9Z6xb93uJiIyxo/+ksS7yLjS1KzG6iv5i78dhVg/X3u5t1H7juRWqVmodIdz6wGVaIApo1u01kmFRdJHVw==}

  '@types/webpack-dev-middleware@5.3.0':
    resolution: {integrity: sha512-SklLlklFBfTyIXo1iWXxzeytjlysWfj5QfIcRJrCc7MgzuCjnZOHXviQwe81iFGq9ZkCUXAg2fpbZdHhj5lSWA==}
    deprecated: This is a stub types definition. webpack-dev-middleware provides its own type definitions, so you do not need this installed.

  '@types/webpack-hot-middleware@2.25.6':
    resolution: {integrity: sha512-1Q9ClNvZR30HIsEAHYQL3bXJK1K7IsrqjGMTfIureFjphsGOZ3TkbeoCupbCmi26pSLjVTPHp+pFrJNpOkBSVg==}

  '@types/ws@8.5.4':
    resolution: {integrity: sha512-zdQDHKUgcX/zBc4GrwsE/7dVdAD8JR4EuiAXiiUhhfyIJXXb2+PrGshFyeXWQPMmmZ2XxgaqclgpIC7eTXc1mg==}

  '@types/yargs-parser@21.0.0':
    resolution: {integrity: sha512-iO9ZQHkZxHn4mSakYV0vFHAVDyEOIJQrV2uZ06HxEPcx+mt8swXoZHIbaaJ2crJYFfErySgktuTZ3BeLz+XmFA==}

  '@types/yargs@17.0.24':
    resolution: {integrity: sha512-6i0aC7jV6QzQB8ne1joVZ0eSFIstHsCrobmOtghM11yGlH0j43FKL2UhWdELkyps0zuf7qVTUVCCR+tgSlyLLw==}

  '@vendia/serverless-express@4.10.4':
    resolution: {integrity: sha512-OH2cX+LqtrayCIkHAkShiLnvrgqGDvwIQEex5dHc/uJitBQjIz3q7dZtfU7cZ5vcR9Vkide5xJQDBEMbXoWLeA==}
    engines: {node: '>=12'}

  '@webassemblyjs/ast@1.11.1':
    resolution: {integrity: sha512-ukBh14qFLjxTQNTXocdyksN5QdM28S1CxHt2rdskFyL+xFV7VremuBLVbmCePj+URalXBENx/9Lm7lnhihtCSw==}

  '@webassemblyjs/floating-point-hex-parser@1.11.1':
    resolution: {integrity: sha512-iGRfyc5Bq+NnNuX8b5hwBrRjzf0ocrJPI6GWFodBFzmFnyvrQ83SHKhmilCU/8Jv67i4GJZBMhEzltxzcNagtQ==}

  '@webassemblyjs/helper-api-error@1.11.1':
    resolution: {integrity: sha512-RlhS8CBCXfRUR/cwo2ho9bkheSXG0+NwooXcc3PAILALf2QLdFyj7KGsKRbVc95hZnhnERon4kW/D3SZpp6Tcg==}

  '@webassemblyjs/helper-buffer@1.11.1':
    resolution: {integrity: sha512-gwikF65aDNeeXa8JxXa2BAk+REjSyhrNC9ZwdT0f8jc4dQQeDQ7G4m0f2QCLPJiMTTO6wfDmRmj/pW0PsUvIcA==}

  '@webassemblyjs/helper-numbers@1.11.1':
    resolution: {integrity: sha512-vDkbxiB8zfnPdNK9Rajcey5C0w+QJugEglN0of+kmO8l7lDb77AnlKYQF7aarZuCrv+l0UvqL+68gSDr3k9LPQ==}

  '@webassemblyjs/helper-wasm-bytecode@1.11.1':
    resolution: {integrity: sha512-PvpoOGiJwXeTrSf/qfudJhwlvDQxFgelbMqtq52WWiXC6Xgg1IREdngmPN3bs4RoO83PnL/nFrxucXj1+BX62Q==}

  '@webassemblyjs/helper-wasm-section@1.11.1':
    resolution: {integrity: sha512-10P9No29rYX1j7F3EVPX3JvGPQPae+AomuSTPiF9eBQeChHI6iqjMIwR9JmOJXwpnn/oVGDk7I5IlskuMwU/pg==}

  '@webassemblyjs/ieee754@1.11.1':
    resolution: {integrity: sha512-hJ87QIPtAMKbFq6CGTkZYJivEwZDbQUgYd3qKSadTNOhVY7p+gfP6Sr0lLRVTaG1JjFj+r3YchoqRYxNH3M0GQ==}

  '@webassemblyjs/leb128@1.11.1':
    resolution: {integrity: sha512-BJ2P0hNZ0u+Th1YZXJpzW6miwqQUGcIHT1G/sf72gLVD9DZ5AdYTqPNbHZh6K1M5VmKvFXwGSWZADz+qBWxeRw==}

  '@webassemblyjs/utf8@1.11.1':
    resolution: {integrity: sha512-9kqcxAEdMhiwQkHpkNiorZzqpGrodQQ2IGrHHxCy+Ozng0ofyMA0lTqiLkVs1uzTRejX+/O0EOT7KxqVPuXosQ==}

  '@webassemblyjs/wasm-edit@1.11.1':
    resolution: {integrity: sha512-g+RsupUC1aTHfR8CDgnsVRVZFJqdkFHpsHMfJuWQzWU3tvnLC07UqHICfP+4XyL2tnr1amvl1Sdp06TnYCmVkA==}

  '@webassemblyjs/wasm-gen@1.11.1':
    resolution: {integrity: sha512-F7QqKXwwNlMmsulj6+O7r4mmtAlCWfO/0HdgOxSklZfQcDu0TpLiD1mRt/zF25Bk59FIjEuGAIyn5ei4yMfLhA==}

  '@webassemblyjs/wasm-opt@1.11.1':
    resolution: {integrity: sha512-VqnkNqnZlU5EB64pp1l7hdm3hmQw7Vgqa0KF/KCNO9sIpI6Fk6brDEiX+iCOYrvMuBWDws0NkTOxYEb85XQHHw==}

  '@webassemblyjs/wasm-parser@1.11.1':
    resolution: {integrity: sha512-rrBujw+dJu32gYB7/Lup6UhdkPx9S9SnobZzRVL7VcBH9Bt9bCBLEuX/YXOOtBsOZ4NQrRykKhffRWHvigQvOA==}

  '@webassemblyjs/wast-printer@1.11.1':
    resolution: {integrity: sha512-IQboUWM4eKzWW+N/jij2sRatKMh99QEelo3Eb2q0qXkvPRISAj8Qxtmw5itwqK+TTkBuUIE45AxYPToqPtL5gg==}

  '@webpack-cli/configtest@2.0.1':
    resolution: {integrity: sha512-njsdJXJSiS2iNbQVS0eT8A/KPnmyH4pv1APj2K0d1wrZcBLw+yppxOy4CGqa0OxDJkzfL/XELDhD8rocnIwB5A==}
    engines: {node: '>=14.15.0'}
    peerDependencies:
      webpack: 5.x.x
      webpack-cli: 5.x.x

  '@webpack-cli/info@2.0.1':
    resolution: {integrity: sha512-fE1UEWTwsAxRhrJNikE7v4EotYflkEhBL7EbajfkPlf6E37/2QshOy/D48Mw8G5XMFlQtS6YV42vtbG9zBpIQA==}
    engines: {node: '>=14.15.0'}
    peerDependencies:
      webpack: 5.x.x
      webpack-cli: 5.x.x

  '@webpack-cli/serve@2.0.1':
    resolution: {integrity: sha512-0G7tNyS+yW8TdgHwZKlDWYXFA6OJQnoLCQvYKkQP0Q2X205PSQ6RNUj0M+1OB/9gRQaUZ/ccYfaxd0nhaWKfjw==}
    engines: {node: '>=14.15.0'}
    peerDependencies:
      webpack: 5.x.x
      webpack-cli: 5.x.x
      webpack-dev-server: '*'
    peerDependenciesMeta:
      webpack-dev-server:
        optional: true

  '@xtuc/ieee754@1.2.0':
    resolution: {integrity: sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==}

  '@xtuc/long@4.2.2':
    resolution: {integrity: sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==}

  abbrev@1.1.1:
    resolution: {integrity: sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==}

  accepts@1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}

  acorn-import-assertions@1.8.0:
    resolution: {integrity: sha512-m7VZ3jwz4eK6A4Vtt8Ew1/mNbP24u0FhdyfA7fSvnJR6LMdfOYnmuIrrJAgrYfYJ10F/otaHTtrtrtmHdMNzEw==}
    peerDependencies:
      acorn: ^8

  acorn-jsx@5.3.2:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn-walk@8.2.0:
    resolution: {integrity: sha512-k+iyHEuPgSw6SbuDpGQM+06HQUa04DZ3o+F6CSzXMvvI5KMvnaEqXe+YVe555R9nn6GPt404fos4wcgpw12SDA==}
    engines: {node: '>=0.4.0'}

  acorn-walk@8.3.2:
    resolution: {integrity: sha512-cjkyv4OtNCIeqhHrfS81QWXoCBPExR/J62oyEqepVw8WaQeSqpW2uhuLPh1m9eWhDuOo/jUXVTlifvesOWp/4A==}
    engines: {node: '>=0.4.0'}

  acorn@7.4.1:
    resolution: {integrity: sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  acorn@8.11.3:
    resolution: {integrity: sha512-Y9rRfJG5jcKOE0CLisYbojUjIrIEE7AGMzA/Sm4BslANhbS+cDMpgBdcPT91oJ7OuJ9hYJBx59RjbhxVnrF8Xg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  acorn@8.8.2:
    resolution: {integrity: sha512-xjIYgE8HBrkpd/sJqOGNspf8uHG+NOHGOw6a/Urj8taM2EXfdNAH2oFcPeIFfsv3+kz/mJrS5VuMqbNLjCa2vw==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  agent-base@6.0.2:
    resolution: {integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==}
    engines: {node: '>= 6.0.0'}

  ajv-formats@2.1.1:
    resolution: {integrity: sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true

  ajv-keywords@3.5.2:
    resolution: {integrity: sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==}
    peerDependencies:
      ajv: ^6.9.1

  ajv-keywords@5.1.0:
    resolution: {integrity: sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==}
    peerDependencies:
      ajv: ^8.8.2

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  ajv@8.12.0:
    resolution: {integrity: sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA==}

  amp-message@0.1.2:
    resolution: {integrity: sha512-JqutcFwoU1+jhv7ArgW38bqrE+LQdcRv4NxNw0mp0JHQyB6tXesWRjtYKlDgHRY2o3JE5UTaBGUK8kSWUdxWUg==}

  amp@0.3.1:
    resolution: {integrity: sha512-OwIuC4yZaRogHKiuU5WlMR5Xk/jAcpPtawWL05Gj8Lvm2F6mwoJt4O/bHI+DHwG79vWd+8OFYM4/BzYqyRd3qw==}

  ansi-colors@4.1.3:
    resolution: {integrity: sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==}
    engines: {node: '>=6'}

  ansi-escapes@3.2.0:
    resolution: {integrity: sha512-cBhpre4ma+U0T1oM5fXg7Dy1Jw7zzwv7lt/GoCpr+hDQJoYnKVPLL4dCvSEFMmQurOQvSrwT7SL/DAlhBI97RQ==}
    engines: {node: '>=4'}

  ansi-html-community@0.0.8:
    resolution: {integrity: sha512-1APHAyr3+PCamwNw3bXCPp4HFLONZt/yIH0sZp0/469KWNTEy+qN5jQ3GVX6DMZ1UXAi34yVwtTeaG/HpBuuzw==}
    engines: {'0': node >= 0.8.0}
    hasBin: true

  ansi-regex@2.1.1:
    resolution: {integrity: sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA==}
    engines: {node: '>=0.10.0'}

  ansi-regex@3.0.1:
    resolution: {integrity: sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==}
    engines: {node: '>=4'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-styles@2.2.1:
    resolution: {integrity: sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA==}
    engines: {node: '>=0.10.0'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  any-promise@1.3.0:
    resolution: {integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==}

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  arg@4.1.3:
    resolution: {integrity: sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==}

  arg@5.0.2:
    resolution: {integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==}

  argparse@1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  array-buffer-byte-length@1.0.0:
    resolution: {integrity: sha512-LPuwb2P+NrQw3XhxGc36+XSvuBPopovXYTR9Ew++Du9Yb/bx5AzBfrIsBoj0EZUifjQU+sHL21sseZ3jerWO/A==}

  array-flatten@1.1.1:
    resolution: {integrity: sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==}

  array-flatten@2.1.2:
    resolution: {integrity: sha512-hNfzcOV8W4NdualtqBFPyVO+54DSJuZGY9qT4pRroB6S9e3iiido2ISIC5h9R2sPJ8H3FHCIiEnsv1lPXO3KtQ==}

  array-includes@3.1.6:
    resolution: {integrity: sha512-sgTbLvL6cNnw24FnbaDyjmvddQ2ML8arZsgaJhoABMoplz/4QRhtrYS+alr1BUM1Bwp6dhx8vVCBSLG+StwOFw==}
    engines: {node: '>= 0.4'}

  array.prototype.flat@1.3.1:
    resolution: {integrity: sha512-roTU0KWIOmJ4DRLmwKd19Otg0/mT3qPNt0Qb3GWW8iObuZXxrjB/pzn0R3hqpRSWg4HCwqx+0vwOnWnvlOyeIA==}
    engines: {node: '>= 0.4'}

  array.prototype.flatmap@1.3.1:
    resolution: {integrity: sha512-8UGn9O1FDVvMNB0UlLv4voxRMze7+FpHyF5mSMRjWHUMlpoDViniy05870VlxhfgTnLbpuwTzvD76MTtWxB/mQ==}
    engines: {node: '>= 0.4'}

  array.prototype.tosorted@1.1.1:
    resolution: {integrity: sha512-pZYPXPRl2PqWcsUs6LOMn+1f1532nEoPTYowBtqLwAW+W8vSVhkIGnmOX1t/UQjD6YGI0vcD2B1U7ZFGQH9jnQ==}

  ast-types@0.13.4:
    resolution: {integrity: sha512-x1FCFnFifvYDDzTaLII71vG5uvDwgtmDTEVWAxrgeiR8VjMONcCXJx7E+USjDtHlwFmt9MysbqgF9b9Vjr6w+w==}
    engines: {node: '>=4'}

  astral-regex@2.0.0:
    resolution: {integrity: sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==}
    engines: {node: '>=8'}

  async-listener@0.6.10:
    resolution: {integrity: sha512-gpuo6xOyF4D5DE5WvyqZdPA3NGhiT6Qf07l7DCB0wwDEsLvDIbCr6j9S5aj5Ch96dLace5tXVzWBZkxU/c5ohw==}
    engines: {node: <=0.11.8 || >0.11.10}

  async@2.6.4:
    resolution: {integrity: sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==}

  async@3.2.4:
    resolution: {integrity: sha512-iAB+JbDEGXhyIUavoDl9WP/Jj106Kz9DEn1DPgYw5ruDn0e3Wgi3sKFm55sASdGBNOQB8F59d9qQ7deqrHA8wQ==}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  autoprefixer@10.4.14:
    resolution: {integrity: sha512-FQzyfOsTlwVzjHxKEqRIAdJx9niO6VCBCoEwax/VLSoQF29ggECcPuBqUMZ+u8jCZOPSy8b8/8KnuFbp0SaFZQ==}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0

  available-typed-arrays@1.0.5:
    resolution: {integrity: sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==}
    engines: {node: '>= 0.4'}

  aws-cdk-lib@2.86.0:
    resolution: {integrity: sha512-76yZ2MawAGXLD3ox4FjhUIPmAMXteGKkeo3tPMthemusDCCkD2X6DBssXBHjB7r9GnrOMMf8JH5BGq2lOZ539g==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      constructs: ^10.0.0
    bundledDependencies:
      - '@balena/dockerignore'
      - case
      - fs-extra
      - ignore
      - jsonschema
      - minimatch
      - punycode
      - semver
      - table
      - yaml

  aws-lambda@1.0.7:
    resolution: {integrity: sha512-9GNFMRrEMG5y3Jvv+V4azWvc+qNWdWLTjDdhf/zgMlz8haaaLWv0xeAIWxz9PuWUBawsVxy0zZotjCdR3Xq+2w==}
    hasBin: true

  aws-sdk@2.1411.0:
    resolution: {integrity: sha512-853lbvI72rQqswidtNQ8+VCh2XiIWmP1g7oDMmDL330bpn8BPLTq+rgyKbax87gHt5W0jHGytrpFeVrYd9CgJg==}
    engines: {node: '>= 10.0.0'}

  axios@0.21.4:
    resolution: {integrity: sha512-ut5vewkiu8jjGBdqpM44XxjuCjq9LAKeHVmoVfHVzy8eHgxxq8SbAVQNovDA8mVi05kP0Ea/n/UzcSHcTJQfNg==}

  axios@1.3.6:
    resolution: {integrity: sha512-PEcdkk7JcdPiMDkvM4K6ZBRYq9keuVJsToxm2zQIM70Qqo2WHTdJZMXcG9X+RmRp2VPNUQC8W1RAGbgt6b1yMg==}

  babel-loader@9.1.2:
    resolution: {integrity: sha512-mN14niXW43tddohGl8HPu5yfQq70iUThvFL/4QzESA7GcZoC0eVOhvWdQ8+3UlSjaDE9MVtsW9mxDY07W7VpVA==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      '@babel/core': ^7.12.0
      webpack: '>=5'

  babel-plugin-polyfill-corejs2@0.3.3:
    resolution: {integrity: sha512-8hOdmFYFSZhqg2C/JgLUQ+t52o5nirNwaWM2B9LWteozwIvM14VSwdsCAUET10qT+kmySAlseadmfeeSWFCy+Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  babel-plugin-polyfill-corejs3@0.6.0:
    resolution: {integrity: sha512-+eHqR6OPcBhJOGgsIar7xoAB1GcSwVUA3XjAd7HJNzOXT4wv6/H7KIdA/Nc60cvUlDbKApmqNvD1B1bzOt4nyA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  babel-plugin-polyfill-regenerator@0.4.1:
    resolution: {integrity: sha512-NtQGmyQDXjQqQ+IzRkBVwEOz9lQ4zxAQZgoAYEtU9dJjnl1Oc98qnN7jcp+bE7O7aYzVpavXE3/VKXNzUbh7aw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  babel-plugin-styled-components@2.1.1:
    resolution: {integrity: sha512-c8lJlszObVQPguHkI+akXv8+Jgb9Ccujx0EetL7oIvwU100LxO6XAGe45qry37wUL40a5U9f23SYrivro2XKhA==}
    peerDependencies:
      styled-components: '>= 2'

  babel-plugin-syntax-jsx@6.18.0:
    resolution: {integrity: sha512-qrPaCSo9c8RHNRHIotaufGbuOBN8rtdC4QrrFFc43vyWCCz7Kl7GL1PGaXtMGQZUXrkCjNEgxDfmAuAabr/rlw==}

  babel-plugin-transform-commonjs@1.1.6:
    resolution: {integrity: sha512-nLPA5wR1LzYmJkYZSjIZVv5Fjqr+lBmgNCGCvUjHKnNGG1OX/kfWggUJECaowEW3D8SaLOyuOU2x4UQw6G5Rwg==}
    peerDependencies:
      '@babel/core': '>=7'

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  basic-auth@2.0.1:
    resolution: {integrity: sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==}
    engines: {node: '>= 0.8'}

  batch@0.6.1:
    resolution: {integrity: sha512-x+VAiMRL6UPkx+kudNvxTl6hB2XNNCG2r+7wixVfIYwu/2HKRXimwQyaumLjMveWvT2Hkd/cAJw+QBMfJ/EKVw==}

  big.js@5.2.2:
    resolution: {integrity: sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==}

  binary-extensions@2.2.0:
    resolution: {integrity: sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==}
    engines: {node: '>=8'}

  blessed@0.1.81:
    resolution: {integrity: sha512-LoF5gae+hlmfORcG1M5+5XZi4LBmvlXTzwJWzUlPryN/SJdSflZvROM2TwkT0GMpq7oqT48NRd4GS7BiVBc5OQ==}
    engines: {node: '>= 0.8.0'}
    hasBin: true

  bodec@0.1.0:
    resolution: {integrity: sha512-Ylo+MAo5BDUq1KA3f3R/MFhh+g8cnHmo8bz3YPGhI1znrMaf77ol1sfvYJzsw3nTE+Y2GryfDxBaR+AqpAkEHQ==}

  body-parser@1.20.1:
    resolution: {integrity: sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  bonjour-service@1.1.1:
    resolution: {integrity: sha512-Z/5lQRMOG9k7W+FkeGTNjh7htqn/2LMnfOvBZ8pynNZCM9MwkQkI3zeI4oz09uWdcgmgHugVvBqxGg4VQJ5PCg==}

  boolbase@1.0.0:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==}

  brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}

  braces@3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==}
    engines: {node: '>=8'}

  browserslist@4.21.5:
    resolution: {integrity: sha512-tUkiguQGW7S3IhB7N+c2MV/HZPSCPAAiYBZXLsBhFB/PCy6ZKKsZrmBayHV9fdGV/ARIfJ14NkxKzRDjvp7L6w==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  buffer@4.9.2:
    resolution: {integrity: sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==}

  builtins@5.0.1:
    resolution: {integrity: sha512-qwVpFEHNfhYJIzNRBvd2C1kyo6jz3ZSMPyyuR47OPdiKWlbYnZNyDWuyR175qDnAJLiCo5fBBqPb3RiXgWlkOQ==}

  bytes@3.0.0:
    resolution: {integrity: sha512-pMhOfFDPiv9t5jjIXkHosWmkSyQbvsgEVNkz0ERHbuLh2T/7j4Mqqpz523Fe8MVY89KC6Sh/QfS2sM+SjgFDcw==}
    engines: {node: '>= 0.8'}

  bytes@3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}

  cache-content-type@1.0.1:
    resolution: {integrity: sha512-IKufZ1o4Ut42YUrZSo8+qnMTrFuKkvyoLXUywKz9GJ5BrhOFGhLdkx9sG4KAnVvbY6kEcSFjLQul+DVmBm2bgA==}
    engines: {node: '>= 6.0.0'}

  call-bind@1.0.2:
    resolution: {integrity: sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  camel-case@4.1.2:
    resolution: {integrity: sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==}

  camelcase-css@2.0.1:
    resolution: {integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==}
    engines: {node: '>= 6'}

  camelcase@6.3.0:
    resolution: {integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==}
    engines: {node: '>=10'}

  camelize@1.0.1:
    resolution: {integrity: sha512-dU+Tx2fsypxTgtLoE36npi3UqcjSSMNYfkqgmoEhtZrraP5VWq0K7FkWVTYa8eMPtnU/G2txVsfdCJTn9uzpuQ==}

  caniuse-api@3.0.0:
    resolution: {integrity: sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw==}

  caniuse-lite@1.0.30001617:
    resolution: {integrity: sha512-mLyjzNI9I+Pix8zwcrpxEbGlfqOkF9kM3ptzmKNw5tizSyYwMe+nGLTqMK9cO+0E+Bh6TsBxNAaHWEM8xwSsmA==}

  case@1.6.3:
    resolution: {integrity: sha512-mzDSXIPaFwVDvZAHqZ9VlbyF4yyXRuX6IvB06WvPYkqJVO24kX1PPhv9bfpKNFZyxYFmmgo03HUiD8iklmJYRQ==}
    engines: {node: '>= 0.8.0'}

  chalk@1.1.3:
    resolution: {integrity: sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A==}
    engines: {node: '>=0.10.0'}

  chalk@2.3.2:
    resolution: {integrity: sha512-ZM4j2/ld/YZDc3Ma8PgN7gyAk+kHMMMyzLNryCPGhWrsfAuDVeuid5bpRFTDgMH9JBK2lA4dyyAkkZYF/WcqDQ==}
    engines: {node: '>=4'}

  chalk@3.0.0:
    resolution: {integrity: sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==}
    engines: {node: '>=8'}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  charm@0.1.2:
    resolution: {integrity: sha512-syedaZ9cPe7r3hoQA9twWYKu5AIyCswN5+szkmPBe9ccdLrj4bYaCnLVPTLd2kgVRc7+zoX4tyPgRnFKCj5YjQ==}

  chokidar@3.5.3:
    resolution: {integrity: sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==}
    engines: {node: '>= 8.10.0'}

  chrome-trace-event@1.0.3:
    resolution: {integrity: sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg==}
    engines: {node: '>=6.0'}

  ci-info@3.8.0:
    resolution: {integrity: sha512-eXTggHWSooYhq49F2opQhuHWgzucfF2YgODK4e1566GQs5BIfP30B0oenwBJHfWxAs2fyPB1s7Mg949zLf61Yw==}
    engines: {node: '>=8'}

  circular-dependency-plugin@5.2.2:
    resolution: {integrity: sha512-g38K9Cm5WRwlaH6g03B9OEz/0qRizI+2I7n+Gz+L5DxXJAPAiWQvwlYNm1V1jkdpUv95bOe/ASm2vfi/G560jQ==}
    engines: {node: '>=6.0.0'}
    peerDependencies:
      webpack: '>=4.0.1'

  classnames@2.3.2:
    resolution: {integrity: sha512-CSbhY4cFEJRe6/GQzIk5qXZ4Jeg5pcsP7b5peFSDpffpe1cqjASH/n9UTjBwOp6XpMSTwQ8Za2K5V02ueA7Tmw==}

  clean-css@5.3.2:
    resolution: {integrity: sha512-JVJbM+f3d3Q704rF4bqQ5UUyTtuJ0JRKNbTKVEeujCCBoMdkEi+V+e8oktO9qGQNSvHrFTM6JZRXrUvGR1czww==}
    engines: {node: '>= 10.0'}

  cli-cursor@2.1.0:
    resolution: {integrity: sha512-8lgKz8LmCRYZZQDpRyT2m5rKJ08TnU4tR9FFFW2rxpxR1FzWi4PQ/NfyODchAatHaUgnSPVcx/R5w6NuTBzFiw==}
    engines: {node: '>=4'}

  cli-tableau@2.0.1:
    resolution: {integrity: sha512-he+WTicka9cl0Fg/y+YyxcN6/bfQ/1O3QmgxRXDhABKqLzvoOSM4fMzp39uMyLBulAFuywD2N7UaoQE7WaADxQ==}
    engines: {node: '>=8.10.0'}

  cliui@8.0.1:
    resolution: {integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==}
    engines: {node: '>=12'}

  clone-deep@4.0.1:
    resolution: {integrity: sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==}
    engines: {node: '>=6'}

  co@4.6.0:
    resolution: {integrity: sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==}
    engines: {iojs: '>= 1.0.0', node: '>= 0.12.0'}

  color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  colord@2.9.3:
    resolution: {integrity: sha512-jeC1axXpnb0/2nn/Y1LPuLdgXBLH7aDcHu4KEKfqw3CUhX7ZpfBSlPKyqXE6btIgEzfWtrX3/tyBCaCvXvMkOw==}

  colorette@2.0.19:
    resolution: {integrity: sha512-3tlv/dIP7FWvj3BsbHrGLJ6l/oKh1O3TcgBqMn+yyCagOxc23fyzDS6HypQbgxWbkpDnf52p1LuR4eWDQ/K9WQ==}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  commander@2.15.1:
    resolution: {integrity: sha512-VlfT9F3V0v+jr4yxPc5gg9s62/fIVWsd2Bk2iD435um1NlGMYdVCq+MjcXnhYq2icNOizHr1kK+5TI6H0Hy0ag==}

  commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  commander@3.0.2:
    resolution: {integrity: sha512-Gar0ASD4BDyKC4hl4DwHqDrmvjoxWKZigVnAbn5H1owvm4CxCPdb0HQDehwNYMJpla5+M2tPmPARzhtYuwpHow==}

  commander@4.1.1:
    resolution: {integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==}
    engines: {node: '>= 6'}

  commander@7.2.0:
    resolution: {integrity: sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==}
    engines: {node: '>= 10'}

  commander@8.3.0:
    resolution: {integrity: sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==}
    engines: {node: '>= 12'}

  commander@9.5.0:
    resolution: {integrity: sha512-KRs7WVDKg86PWiuAqhDrAQnTXZKraVcCc6vFdL14qrZ/DcWwuRo7VoiYXalXO7S5GKpqYiVEwCbgFDfxNHKJBQ==}
    engines: {node: ^12.20.0 || >=14}

  common-path-prefix@3.0.0:
    resolution: {integrity: sha512-QE33hToZseCH3jS0qN96O/bSh3kaw/h+Tq7ngyY9eWDUnTlTNUyqfqvCXioLe5Na5jFsL78ra/wuBU4iuEgd4w==}

  commondir@1.0.1:
    resolution: {integrity: sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg==}

  compressible@2.0.18:
    resolution: {integrity: sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==}
    engines: {node: '>= 0.6'}

  compression@1.7.4:
    resolution: {integrity: sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==}
    engines: {node: '>= 0.8.0'}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  connect-history-api-fallback@2.0.0:
    resolution: {integrity: sha512-U73+6lQFmfiNPrYbXqr6kZ1i1wiRqXnp2nhMsINseWXO8lDau0LGEffJ8kQi4EjLZympVgRdvqjAgiZ1tgzDDA==}
    engines: {node: '>=0.8'}

  connect-pause@0.1.1:
    resolution: {integrity: sha512-a1gSWQBQD73krFXdUEYJom2RTFrWUL3YvXDCRkyv//GVXc79cdW9MngtRuN9ih4FDKBtfJAJId+BbDuX+1rh2w==}

  consola@2.15.3:
    resolution: {integrity: sha512-9vAdYbHj6x2fLKC4+oPH0kFzY/orMZyG2Aj+kNylHxKGJ/Ed4dpNyAQYwJOdqO4zdM7XpVHmyejQDcQHrnuXbw==}

  constructs@10.2.69:
    resolution: {integrity: sha512-0AiM/uQe5Uk6JVe/62oolmSN2MjbFQkOlYrM3fFGZLKuT+g7xlAI10EebFhyCcZwI2JAcWuWCmmCAyCothxjuw==}
    engines: {node: '>= 16.14.0'}

  content-disposition@0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==}
    engines: {node: '>= 0.6'}

  content-type@1.0.5:
    resolution: {integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==}
    engines: {node: '>= 0.6'}

  continuation-local-storage@3.2.1:
    resolution: {integrity: sha512-jx44cconVqkCEEyLSKWwkvUXwO561jXMa3LPjTPsm5QR22PA0/mhe33FT4Xb5y74JDvt/Cq+5lm8S8rskLv9ZA==}

  convert-source-map@1.9.0:
    resolution: {integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==}

  cookie-signature@1.0.6:
    resolution: {integrity: sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==}

  cookie@0.5.0:
    resolution: {integrity: sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==}
    engines: {node: '>= 0.6'}

  cookies@0.8.0:
    resolution: {integrity: sha512-8aPsApQfebXnuI+537McwYsDtjVxGm8gTIzQI3FDW6t5t/DAhERxtnbEPN/8RX+uZthoz4eCOgloXaE5cYyNow==}
    engines: {node: '>= 0.8'}

  copy-anything@2.0.6:
    resolution: {integrity: sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==}

  copy-webpack-plugin@11.0.0:
    resolution: {integrity: sha512-fX2MWpamkW0hZxMEg0+mYnA40LTosOSa5TqZ9GYIBzyJa9C3QUaMPSE2xAi/buNr8u89SfD9wHSQVBzrRa/SOQ==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      webpack: ^5.1.0

  core-js-compat@3.30.0:
    resolution: {integrity: sha512-P5A2h/9mRYZFIAP+5Ab8ns6083IyVpSclU74UNvbGVQ8VM7n3n3/g2yF3AkKQ9NXz2O+ioxLbEWKnDtgsFamhg==}

  core-js-pure@3.30.0:
    resolution: {integrity: sha512-+2KbMFGeBU0ln/csoPqTe0i/yfHbrd2EUhNMObsGtXMKS/RTtlkYyi+/3twLcevbgNR0yM/r0Psa3TEoQRpFMQ==}

  core-js@3.30.1:
    resolution: {integrity: sha512-ZNS5nbiSwDTq4hFosEDqm65izl2CWmLz0hARJMyNQBgkUZMIF51cQiMvIQKA6hvuaeWxQDP3hEedM1JZIgTldQ==}

  core-util-is@1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==}

  cors@2.8.5:
    resolution: {integrity: sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==}
    engines: {node: '>= 0.10'}

  cosmiconfig-typescript-loader@4.3.0:
    resolution: {integrity: sha512-NTxV1MFfZDLPiBMjxbHRwSh5LaLcPMwNdCutmnHJCKoVnlvldPWlllonKwrsRJ5pYZBIBGRWWU2tfvzxgeSW5Q==}
    engines: {node: '>=12', npm: '>=6'}
    peerDependencies:
      '@types/node': '*'
      cosmiconfig: '>=7'
      ts-node: '>=10'
      typescript: '>=3'

  cosmiconfig@7.1.0:
    resolution: {integrity: sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==}
    engines: {node: '>=10'}

  cosmiconfig@8.1.3:
    resolution: {integrity: sha512-/UkO2JKI18b5jVMJUp0lvKFMpa/Gye+ZgZjKD+DGEN9y7NRcf/nK1A0sp67ONmKtnDCNMS44E6jrk0Yc3bDuUw==}
    engines: {node: '>=14'}

  create-require@1.1.1:
    resolution: {integrity: sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==}

  croner@4.1.97:
    resolution: {integrity: sha512-/f6gpQuxDaqXu+1kwQYSckUglPaOrHdbIlBAu0YuW8/Cdb45XwXYNUBXg3r/9Mo6n540Kn/smKcZWko5x99KrQ==}

  cross-spawn@7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}

  css-blank-pseudo@3.0.3:
    resolution: {integrity: sha512-VS90XWtsHGqoM0t4KpH053c4ehxZ2E6HtGI7x68YFV0pTo/QmkV/YFA+NnlvK8guxZVNWGQhVNJGC39Q8XF4OQ==}
    engines: {node: ^12 || ^14 || >=16}
    hasBin: true
    peerDependencies:
      postcss: ^8.4

  css-color-keywords@1.0.0:
    resolution: {integrity: sha512-FyyrDHZKEjXDpNJYvVsV960FiqQyXc/LlYmsxl2BcdMb2WPx0OGRVgTg55rPSyLSNMqP52R9r8geSp7apN3Ofg==}
    engines: {node: '>=4'}

  css-declaration-sorter@6.4.0:
    resolution: {integrity: sha512-jDfsatwWMWN0MODAFuHszfjphEXfNw9JUAhmY4pLu3TyTU+ohUpsbVtbU+1MZn4a47D9kqh03i4eyOm+74+zew==}
    engines: {node: ^10 || ^12 || >=14}
    peerDependencies:
      postcss: ^8.0.9

  css-has-pseudo@3.0.4:
    resolution: {integrity: sha512-Vse0xpR1K9MNlp2j5w1pgWIJtm1a8qS0JwS9goFYcImjlHEmywP9VUF05aGBXzGpDJF86QXk4L0ypBmwPhGArw==}
    engines: {node: ^12 || ^14 || >=16}
    hasBin: true
    peerDependencies:
      postcss: ^8.4

  css-loader@6.7.3:
    resolution: {integrity: sha512-qhOH1KlBMnZP8FzRO6YCH9UHXQhVMcEGLyNdb7Hv2cpcmJbW0YrddO+tG1ab5nT41KpHIYGsbeHqxB9xPu1pKQ==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^5.0.0

  css-minimizer-webpack-plugin@4.2.2:
    resolution: {integrity: sha512-s3Of/4jKfw1Hj9CxEO1E5oXhQAxlayuHO2y/ML+C6I9sQ7FdzfEV6QgMLN3vI+qFsjJGIAFLKtQK7t8BOXAIyA==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      '@parcel/css': '*'
      '@swc/css': '*'
      clean-css: '*'
      csso: '*'
      esbuild: '*'
      lightningcss: '*'
      webpack: ^5.0.0
    peerDependenciesMeta:
      '@parcel/css':
        optional: true
      '@swc/css':
        optional: true
      clean-css:
        optional: true
      csso:
        optional: true
      esbuild:
        optional: true
      lightningcss:
        optional: true

  css-prefers-color-scheme@6.0.3:
    resolution: {integrity: sha512-4BqMbZksRkJQx2zAjrokiGMd07RqOa2IxIrrN10lyBe9xhn9DEvjUK79J6jkeiv9D9hQFXKb6g1jwU62jziJZA==}
    engines: {node: ^12 || ^14 || >=16}
    hasBin: true
    peerDependencies:
      postcss: ^8.4

  css-select@4.3.0:
    resolution: {integrity: sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==}

  css-to-react-native@3.2.0:
    resolution: {integrity: sha512-e8RKaLXMOFii+02mOlqwjbD00KSEKqblnpO9e++1aXS1fPQOpS1YoqdVHBqPjHNoxeF2mimzVqawm2KCbEdtHQ==}

  css-tree@1.1.3:
    resolution: {integrity: sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==}
    engines: {node: '>=8.0.0'}

  css-what@6.1.0:
    resolution: {integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==}
    engines: {node: '>= 6'}

  cssdb@7.5.3:
    resolution: {integrity: sha512-NQNRhrEnS6cW+RU/foLphb6xI/MDA70bI3Cy6VxJU8ilxgyTYz1X9zUzFGVTG5nGPylcKAGIt/UNc4deT56lQQ==}

  cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  cssnano-preset-default@5.2.14:
    resolution: {integrity: sha512-t0SFesj/ZV2OTylqQVOrFgEh5uanxbO6ZAdeCrNsUQ6fVuXwYTxJPNAGvGTxHbD68ldIJNec7PyYZDBrfDQ+6A==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  cssnano-utils@3.1.0:
    resolution: {integrity: sha512-JQNR19/YZhz4psLX/rQ9M83e3z2Wf/HdJbryzte4a3NSuafyp9w/I4U+hx5C2S9g41qlstH7DEWnZaaj83OuEA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  cssnano@5.1.15:
    resolution: {integrity: sha512-j+BKgDcLDQA+eDifLx0EO4XSA56b7uut3BQFH+wbSaSTuGLuiyTa/wbRYthUXX8LC9mLg+WWKe8h+qJuwTAbHw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  csso@4.2.0:
    resolution: {integrity: sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA==}
    engines: {node: '>=8.0.0'}

  csstype@3.1.2:
    resolution: {integrity: sha512-I7K1Uu0MBPzaFKg4nI5Q7Vs2t+3gWWW648spaF+Rg7pI9ds18Ugn+lvg4SHczUdKlHI5LWBXyqfS8+DufyBsgQ==}

  culvert@0.1.2:
    resolution: {integrity: sha512-yi1x3EAWKjQTreYWeSd98431AV+IEE0qoDyOoaHJ7KJ21gv6HtBXHVLX74opVSGqcR8/AbjJBHAHpcOy2bj5Gg==}

  data-uri-to-buffer@3.0.1:
    resolution: {integrity: sha512-WboRycPNsVw3B3TL559F7kuBUM4d8CgMEvk6xEJlOp7OBPjt6G7z8WMWlD2rOFZLk6OYfFIUGsCOWzcQH9K2og==}
    engines: {node: '>= 6'}

  dayjs@1.11.9:
    resolution: {integrity: sha512-QvzAURSbQ0pKdIye2txOzNaHmxtUBXerpY0FJsFXUMKbIZeFm5ht1LS/jFsrncjnmtv8HsG0W2g6c0zUjZWmpA==}

  dayjs@1.8.36:
    resolution: {integrity: sha512-3VmRXEtw7RZKAf+4Tv1Ym9AGeo8r8+CjDi26x+7SYQil1UqtqdaokhzoEJohqlzt0m5kacJSDhJQkG/LWhpRBw==}

  debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@3.1.0:
    resolution: {integrity: sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@3.2.7:
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  deep-equal@1.0.1:
    resolution: {integrity: sha512-bHtC0iYvWhyaTzvV3CZgPeZQqCOBGyGsVV7v4eevpdkLHfiSrXUdBG+qAuSz4RI70sszvjQ1QSZ98An1yNwpSw==}

  deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  deepmerge@4.3.1:
    resolution: {integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==}
    engines: {node: '>=0.10.0'}

  default-gateway@6.0.3:
    resolution: {integrity: sha512-fwSOJsbbNzZ/CUFpqFBqYfYNLj1NbMPm8MMCIzHjC83iSJRBEGmDUxU+WP661BaBQImeC2yHwXtz+P/O9o+XEg==}
    engines: {node: '>= 10'}

  define-lazy-prop@2.0.0:
    resolution: {integrity: sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==}
    engines: {node: '>=8'}

  define-properties@1.2.0:
    resolution: {integrity: sha512-xvqAVKGfT1+UAvPwKTVw/njhdQ8ZhXK4lI0bCIuCMrp2up9nPnaDftrLtmpTazqd1o+UY4zgzU+avtMbDP+ldA==}
    engines: {node: '>= 0.4'}

  degenerator@3.0.4:
    resolution: {integrity: sha512-Z66uPeBfHZAHVmue3HPfyKu2Q0rC2cRxbTOsvmU/po5fvvcx27W4mIu9n0PUlQih4oUYvcG1BsbtVv8x7KDOSw==}
    engines: {node: '>= 6'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  delegates@1.0.0:
    resolution: {integrity: sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==}

  depd@1.1.2:
    resolution: {integrity: sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==}
    engines: {node: '>= 0.6'}

  depd@2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}

  destroy@1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  detect-node@2.1.0:
    resolution: {integrity: sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g==}

  didyoumean@1.2.2:
    resolution: {integrity: sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==}

  diff@4.0.2:
    resolution: {integrity: sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==}
    engines: {node: '>=0.3.1'}

  dir-glob@3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}

  dlv@1.1.3:
    resolution: {integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==}

  dns-equal@1.0.0:
    resolution: {integrity: sha512-z+paD6YUQsk+AbGCEM4PrOXSss5gd66QfcVBFTKR/HpFL9jCqikS94HYwKww6fQyO7IxrIIyUu+g0Ka9tUS2Cg==}

  dns-packet@5.5.0:
    resolution: {integrity: sha512-USawdAUzRkV6xrqTjiAEp6M9YagZEzWcSUaZTcIFAiyQWW1SoI6KyId8y2+/71wbgHKQAKd+iupLv4YvEwYWvA==}
    engines: {node: '>=6'}

  doctrine@2.1.0:
    resolution: {integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==}
    engines: {node: '>=0.10.0'}

  doctrine@3.0.0:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==}
    engines: {node: '>=6.0.0'}

  dom-align@1.12.4:
    resolution: {integrity: sha512-R8LUSEay/68zE5c8/3BDxiTEvgb4xZTF0RKmAHfiEVN3klfIpXfi2/QCoiWPccVQ0J/ZGdz9OjzL4uJEP/MRAw==}

  dom-converter@0.2.0:
    resolution: {integrity: sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA==}

  dom-serializer@1.4.1:
    resolution: {integrity: sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==}

  domelementtype@2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}

  domhandler@4.3.1:
    resolution: {integrity: sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==}
    engines: {node: '>= 4'}

  domutils@2.8.0:
    resolution: {integrity: sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A==}

  dot-case@3.0.4:
    resolution: {integrity: sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==}

  duplexer@0.1.2:
    resolution: {integrity: sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg==}

  ee-first@1.1.1:
    resolution: {integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==}

  electron-to-chromium@1.4.355:
    resolution: {integrity: sha512-056hxzEE4l667YeOccgjhRr5fTiwZ6EIJ4FpzGps4k3YcS8iAhiaBYUBrv5E2LDQJsussscv9EEUwAYKnv+ZKg==}

  emitter-listener@1.1.2:
    resolution: {integrity: sha512-Bt1sBAGFHY9DKY+4/2cV6izcKJUf5T7/gkdmkxzX/qv9CcGH8xSwVRW5mtX03SWJtRTWSOpzCuWN9rBFYZepZQ==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  emojis-list@3.0.0:
    resolution: {integrity: sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==}
    engines: {node: '>= 4'}

  encodeurl@1.0.2:
    resolution: {integrity: sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==}
    engines: {node: '>= 0.8'}

  enhanced-resolve@5.12.0:
    resolution: {integrity: sha512-QHTXI/sZQmko1cbDoNAa3mJ5qhWUUNAq3vR0/YiD379fWQrcfuoX1+HW2S0MTt7XmoPLapdaDKUtelUSPic7hQ==}
    engines: {node: '>=10.13.0'}

  enquirer@2.3.6:
    resolution: {integrity: sha512-yjNnPr315/FjS4zIsUxYguYUPP2e1NK4d7E7ZOLiyYCcbFBiTMyID+2wvm2w6+pZ/odMA7cRkjhsPbltwBOrLg==}
    engines: {node: '>=8.6'}

  entities@2.2.0:
    resolution: {integrity: sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==}

  entities@4.4.0:
    resolution: {integrity: sha512-oYp7156SP8LkeGD0GF85ad1X9Ai79WtRsZ2gxJqtBuzH+98YUV6jkHEKlZkMbcrjJjIVJNIDP/3WL9wQkoPbWA==}
    engines: {node: '>=0.12'}

  envinfo@7.8.1:
    resolution: {integrity: sha512-/o+BXHmB7ocbHEAs6F2EnG0ogybVVUdkRunTT2glZU9XAaGmhqskrvKwqXuDfNjEO0LZKWdejEEpnq8aM0tOaw==}
    engines: {node: '>=4'}
    hasBin: true

  errno@0.1.8:
    resolution: {integrity: sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==}
    hasBin: true

  error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}

  error-stack-parser@2.1.4:
    resolution: {integrity: sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==}

  errorhandler@1.5.1:
    resolution: {integrity: sha512-rcOwbfvP1WTViVoUjcfZicVzjhjTuhSMntHh6mW3IrEiyE6mJyXvsToJUJGlGlw/2xU9P5whlWNGlIDVeCiT4A==}
    engines: {node: '>= 0.8'}

  es-abstract@1.21.2:
    resolution: {integrity: sha512-y/B5POM2iBnIxCiernH1G7rC9qQoM77lLIMQLuob0zhp8C56Po81+2Nj0WFKnd0pNReDTnkYryc+zhOzpEIROg==}
    engines: {node: '>= 0.4'}

  es-module-lexer@0.9.3:
    resolution: {integrity: sha512-1HQ2M2sPtxwnvOvT1ZClHyQDiggdNjURWpY2we6aMKCQiUVxTmVs2UYPLIrD84sS+kMdUwfBSylbJPwNnBrnHQ==}

  es-set-tostringtag@2.0.1:
    resolution: {integrity: sha512-g3OMbtlwY3QewlqAiMLI47KywjWZoEytKr8pf6iTC8uJq5bIAH52Z9pnQ8pVL6whrCto53JZDuUIsifGeLorTg==}
    engines: {node: '>= 0.4'}

  es-shim-unscopables@1.0.0:
    resolution: {integrity: sha512-Jm6GPcCdC30eMLbZ2x8z2WuRwAws3zTBBKuusffYVUrNj/GVSUAZ+xKMaUpfNDR5IbyNA5LJbaecoUVbmUcB1w==}

  es-to-primitive@1.2.1:
    resolution: {integrity: sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==}
    engines: {node: '>= 0.4'}

  escalade@3.1.1:
    resolution: {integrity: sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==}
    engines: {node: '>=6'}

  escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}

  escodegen@1.14.3:
    resolution: {integrity: sha512-qFcX0XJkdg+PB3xjZZG/wKSuT1PnQWx57+TVSjIMmILd2yC/6ByYElPwJnslDsuWuSAp4AwJGumarAAmJch5Kw==}
    engines: {node: '>=4.0'}
    hasBin: true

  eslint-config-prettier@8.8.0:
    resolution: {integrity: sha512-wLbQiFre3tdGgpDv67NQKnJuTlcUVYHas3k+DZCc2U2BadthoEY4B7hLPvAxaqdyOGCzuLfii2fqGph10va7oA==}
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-config-standard-jsx@11.0.0:
    resolution: {integrity: sha512-+1EV/R0JxEK1L0NGolAr8Iktm3Rgotx3BKwgaX+eAuSX8D952LULKtjgZD3F+e6SvibONnhLwoTi9DPxN5LvvQ==}
    peerDependencies:
      eslint: ^8.8.0
      eslint-plugin-react: ^7.28.0

  eslint-config-standard@17.0.0:
    resolution: {integrity: sha512-/2ks1GKyqSOkH7JFvXJicu0iMpoojkwB+f5Du/1SC0PtBL+s8v30k9njRZ21pm2drKYm2342jFnGWzttxPmZVg==}
    peerDependencies:
      eslint: ^8.0.1
      eslint-plugin-import: ^2.25.2
      eslint-plugin-n: ^15.0.0
      eslint-plugin-promise: ^6.0.0

  eslint-import-resolver-node@0.3.7:
    resolution: {integrity: sha512-gozW2blMLJCeFpBwugLTGyvVjNoeo1knonXAcatC6bjPBZitotxdWf7Gimr25N4c0AAOo4eOUfaG82IJPDpqCA==}

  eslint-module-utils@2.8.0:
    resolution: {integrity: sha512-aWajIYfsqCKRDgUfjEXNN/JlrzauMuSEy5sbd7WXbtW3EH6A6MpwEh42c7qD+MqQo9QMJ6fWLAeIJynx0g6OAw==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: '*'
      eslint-import-resolver-node: '*'
      eslint-import-resolver-typescript: '*'
      eslint-import-resolver-webpack: '*'
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
      eslint:
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true

  eslint-plugin-es@4.1.0:
    resolution: {integrity: sha512-GILhQTnjYE2WorX5Jyi5i4dz5ALWxBIdQECVQavL6s7cI76IZTDWleTHkxz/QT3kvcs2QlGHvKLYsSlPOlPXnQ==}
    engines: {node: '>=8.10.0'}
    peerDependencies:
      eslint: '>=4.19.1'

  eslint-plugin-import@2.27.5:
    resolution: {integrity: sha512-LmEt3GVofgiGuiE+ORpnvP+kAm3h6MLZJ4Q5HCyHADofsb4VzXFsRiWj3c0OFiV+3DWFh0qg3v9gcPlfc3zRow==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true

  eslint-plugin-n@15.7.0:
    resolution: {integrity: sha512-jDex9s7D/Qial8AGVIHq4W7NswpUD5DPDL2RH8Lzd9EloWUuvUkHfv4FRLMipH5q2UtyurorBkPeNi1wVWNh3Q==}
    engines: {node: '>=12.22.0'}
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-plugin-prettier@4.2.1:
    resolution: {integrity: sha512-f/0rXLXUt0oFYs8ra4w49wYZBG5GKZpAYsJSm6rnYL5uVDjd+zowwMwVZHnAjf4edNrKpCDYfXDgmRE/Ak7QyQ==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      eslint: '>=7.28.0'
      eslint-config-prettier: '*'
      prettier: '>=2.0.0'
    peerDependenciesMeta:
      eslint-config-prettier:
        optional: true

  eslint-plugin-promise@6.1.1:
    resolution: {integrity: sha512-tjqWDwVZQo7UIPMeDReOpUgHCmCiH+ePnVT+5zVapL0uuHnegBUs2smM13CzOs2Xb5+MHMRFTs9v24yjba4Oig==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0

  eslint-plugin-react@7.32.2:
    resolution: {integrity: sha512-t2fBMa+XzonrrNkyVirzKlvn5RXzzPwRHtMvLAtVZrt8oxgnTQaYbU6SXTOO1mwQgp1y5+toMSKInnzGr0Knqg==}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8

  eslint-scope@5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==}
    engines: {node: '>=8.0.0'}

  eslint-scope@7.2.0:
    resolution: {integrity: sha512-DYj5deGlHBfMt15J7rdtyKNq/Nqlv5KfU4iodrQ019XESsRnwXH9KAE0y3cwtUHDo2ob7CypAnCqefh6vioWRw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-utils@2.1.0:
    resolution: {integrity: sha512-w94dQYoauyvlDc43XnGB8lU3Zt713vNChgt4EWwhXAP2XkBvndfxF0AgIqKOOasjPIPzj9JqgwkwbCYD0/V3Zg==}
    engines: {node: '>=6'}

  eslint-utils@3.0.0:
    resolution: {integrity: sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA==}
    engines: {node: ^10.0.0 || ^12.0.0 || >= 14.0.0}
    peerDependencies:
      eslint: '>=5'

  eslint-visitor-keys@1.3.0:
    resolution: {integrity: sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==}
    engines: {node: '>=4'}

  eslint-visitor-keys@2.1.0:
    resolution: {integrity: sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==}
    engines: {node: '>=10'}

  eslint-visitor-keys@3.4.0:
    resolution: {integrity: sha512-HPpKPUBQcAsZOsHAFwTtIKcYlCje62XB7SEAcxjtmW6TD1WVpkS6i6/hOVtTZIl4zGj/mBqpFVGvaDneik+VoQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-webpack-plugin@4.0.0:
    resolution: {integrity: sha512-eM9ccGRWkU+btBSVfABRn8CjT7jZ2Q+UV/RfErMDVCFXpihEbvajNrLltZpwTAcEoXSqESGlEPIUxl7PoDlLWw==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      eslint: ^8.0.0
      webpack: ^5.0.0

  eslint@7.32.0:
    resolution: {integrity: sha512-VHZ8gX+EDfz+97jGcgyGCyRia/dPOd6Xh9yPv8Bl1+SoaIwD+a/vlrOmGRUyOYu7MwUhc7CxqeaDZU13S4+EpA==}
    engines: {node: ^10.12.0 || >=12.0.0}
    hasBin: true

  eslint@8.38.0:
    resolution: {integrity: sha512-pIdsD2jwlUGf/U38Jv97t8lq6HpaU/G9NKbYmpWpZGw3LdTNhZLbJePqxOXGB5+JEKfOPU/XLxYxFh03nr1KTg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    hasBin: true

  espree@7.3.1:
    resolution: {integrity: sha512-v3JCNCE64umkFpmkFGqzVKsOT0tN1Zr+ueqLZfpV1Ob8e+CEgPWa+OxCoGH3tnhimMKIaBm4m/vaRpJ/krRz2g==}
    engines: {node: ^10.12.0 || >=12.0.0}

  espree@9.5.1:
    resolution: {integrity: sha512-5yxtHSZXRSW5pvv3hAlXM5+/Oswi1AUFqBmbibKb5s6bp3rGIDkyXU6xCoyuuLhijr4SFwPrXRoZjz0AZDN9tg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true

  esquery@1.5.0:
    resolution: {integrity: sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}

  estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  etag@1.8.1:
    resolution: {integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==}
    engines: {node: '>= 0.6'}

  eventemitter2@0.4.14:
    resolution: {integrity: sha512-K7J4xq5xAD5jHsGM5ReWXRTFa3JRGofHiMcVgQ8PRwgWxzjHpMWCIzsmyf60+mh8KLsqYPcjUMa0AC4hd6lPyQ==}

  eventemitter2@5.0.1:
    resolution: {integrity: sha512-5EM1GHXycJBS6mauYAbVKT1cVs7POKWb2NXD4Vyt8dDqeZa7LaDK1/sjtL+Zb0lzTpSNil4596Dyu97hz37QLg==}

  eventemitter2@6.4.9:
    resolution: {integrity: sha512-JEPTiaOt9f04oa6NOkc4aH+nVp5I3wEjpHbIPqfgCdD5v5bUzy7xQqwcVO2aDQgOWhI28da57HksMrzK9HlRxg==}

  eventemitter3@4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==}

  events@1.1.1:
    resolution: {integrity: sha512-kEcvvCBByWXGnZy6JUlgAp2gBIUjfCAV6P6TgT1/aaQKcmuAEC4OZTV1I4EWQLz2gxZw76atuVyvHhTxvi0Flw==}
    engines: {node: '>=0.4.x'}

  events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}

  execa@5.1.1:
    resolution: {integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==}
    engines: {node: '>=10'}

  express-urlrewrite@1.4.0:
    resolution: {integrity: sha512-PI5h8JuzoweS26vFizwQl6UTF25CAHSggNv0J25Dn/IKZscJHWZzPrI5z2Y2jgOzIaw2qh8l6+/jUcig23Z2SA==}

  express@4.18.2:
    resolution: {integrity: sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ==}
    engines: {node: '>= 0.10.0'}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-diff@1.2.0:
    resolution: {integrity: sha512-xJuoT5+L99XlZ8twedaRf6Ax2TgQVxvgZOYoPKqZufmJib0tL2tegPBOZb1pVNgIhlqDlA0eO0c3wBvQcmzx4w==}

  fast-glob@3.2.12:
    resolution: {integrity: sha512-DVj4CQIYYow0BlaelwK1pHl5n5cRSJfM60UA0zK891sVInoPri2Ekj7+e1CT3/3qxXenpI+nBBmQAcJPJgaj4w==}
    engines: {node: '>=8.6.0'}

  fast-json-patch@3.1.1:
    resolution: {integrity: sha512-vf6IHUX2SBcA+5/+4883dsIjpBTqmfBjmYiWK1savxQmFk4JfBMLa7ynTYOs1Rolp/T1betJxHiGD3g1Mn8lUQ==}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  fastest-levenshtein@1.0.16:
    resolution: {integrity: sha512-eRnCtTTtGZFpQCwhJiUOuxPQWRXVKYDn0b2PeHfXL6/Zi53SLAzAHfVhVWK2AryC/WH05kGfxhFIPvTF0SXQzg==}
    engines: {node: '>= 4.9.1'}

  fastq@1.15.0:
    resolution: {integrity: sha512-wBrocU2LCXXa+lWBt8RoIRD89Fi8OdABODa/kEnyeyjS5aZO5/GNvI5sEINADqP/h8M29UHTHUb53sUu5Ihqdw==}

  faye-websocket@0.11.4:
    resolution: {integrity: sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g==}
    engines: {node: '>=0.8.0'}

  fclone@1.0.11:
    resolution: {integrity: sha512-GDqVQezKzRABdeqflsgMr7ktzgF9CyS+p2oe0jJqUY6izSSbhPIQJDpoU4PtGcD7VPM9xh/dVrTu6z1nwgmEGw==}

  figures@2.0.0:
    resolution: {integrity: sha512-Oa2M9atig69ZkfwiApY8F2Yy+tzMbazyvqv21R0NsSC8floSOC09BbT1ITWAdoMGQvJ/aZnR1KMwdx9tvHnTNA==}
    engines: {node: '>=4'}

  file-entry-cache@6.0.1:
    resolution: {integrity: sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==}
    engines: {node: ^10.12.0 || >=12.0.0}

  file-loader@6.2.0:
    resolution: {integrity: sha512-qo3glqyTa61Ytg4u73GultjHGjdRyig3tG6lPtyX/jOEJvHif9uB0/OCI2Kif6ctF3caQTW2G5gym21oAsI4pw==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0

  file-uri-to-path@2.0.0:
    resolution: {integrity: sha512-hjPFI8oE/2iQPVe4gbrJ73Pp+Xfub2+WI2LlXDbsaJBwT5wuMh35WNWVYYTpnz895shtwfyutMFLFywpQAFdLg==}
    engines: {node: '>= 6'}

  fill-range@7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==}
    engines: {node: '>=8'}

  finalhandler@1.2.0:
    resolution: {integrity: sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==}
    engines: {node: '>= 0.8'}

  find-cache-dir@3.3.2:
    resolution: {integrity: sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig==}
    engines: {node: '>=8'}

  find-up@3.0.0:
    resolution: {integrity: sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==}
    engines: {node: '>=6'}

  find-up@4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}

  find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}

  flat-cache@3.0.4:
    resolution: {integrity: sha512-dm9s5Pw7Jc0GvMYbshN6zchCA9RgQlzzEZX3vylR9IqFfS8XciblUXOKfW6SiuJ0e13eDYZoZV5wdrev7P3Nwg==}
    engines: {node: ^10.12.0 || >=12.0.0}

  flatted@3.2.7:
    resolution: {integrity: sha512-5nqDSxl8nn5BSNxyR3n4I6eDmbolI6WT+QqR547RwxQapgjQBmtktdP+HTBb/a/zLsbzERTONyUB5pefh5TtjQ==}

  follow-redirects@1.15.2:
    resolution: {integrity: sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  for-each@0.3.3:
    resolution: {integrity: sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==}

  fork-ts-checker-webpack-plugin@7.3.0:
    resolution: {integrity: sha512-IN+XTzusCjR5VgntYFgxbxVx3WraPRnKehBFrf00cMSrtUuW9MsG9dhL6MWpY6MkjC3wVwoujfCDgZZCQwbswA==}
    engines: {node: '>=12.13.0', yarn: '>=1.0.0'}
    peerDependencies:
      typescript: '>3.6.0'
      vue-template-compiler: '*'
      webpack: ^5.11.0
    peerDependenciesMeta:
      vue-template-compiler:
        optional: true

  form-data@4.0.0:
    resolution: {integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==}
    engines: {node: '>= 6'}

  forwarded@0.2.0:
    resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==}
    engines: {node: '>= 0.6'}

  fraction.js@4.2.0:
    resolution: {integrity: sha512-MhLuK+2gUcnZe8ZHlaaINnQLl0xRIGRfcGk2yl8xoQAfHrSsL3rYu6FCmBdkdbhc9EPlwyGHewaRsvwRMJtAlA==}

  fresh@0.5.2:
    resolution: {integrity: sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==}
    engines: {node: '>= 0.6'}

  friendly-errors-webpack-plugin@1.7.0:
    resolution: {integrity: sha512-K27M3VK30wVoOarP651zDmb93R9zF28usW4ocaK3mfQeIEI5BPht/EzZs5E8QLLwbLRJQMwscAjDxYPb1FuNiw==}
    peerDependencies:
      webpack: ^2.0.0 || ^3.0.0 || ^4.0.0

  fs-extra@10.1.0:
    resolution: {integrity: sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==}
    engines: {node: '>=12'}

  fs-extra@11.1.1:
    resolution: {integrity: sha512-MGIE4HOvQCeUCzmlHs0vXpih4ysz4wg9qiSAu6cd42lVwPbTM1TjV7RusoyQqMmk/95gdQZX72u+YW+c3eEpFQ==}
    engines: {node: '>=14.14'}

  fs-extra@8.1.0:
    resolution: {integrity: sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==}
    engines: {node: '>=6 <7 || >=8'}

  fs-monkey@1.0.3:
    resolution: {integrity: sha512-cybjIfiiE+pTWicSCLFHSrXZ6EilF30oh91FDP9S2B051prEa7QWfrVTQm10/dDpswBDXZugPa1Ogu8Yh+HV0Q==}

  fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  ftp@0.3.10:
    resolution: {integrity: sha512-faFVML1aBx2UoDStmLwv2Wptt4vw5x03xxX172nhA5Y5HBshW5JweqQ2W4xL4dezQTG8inJsuYcpPHHU3X5OTQ==}
    engines: {node: '>=0.8.0'}

  function-bind@1.1.1:
    resolution: {integrity: sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==}

  function.prototype.name@1.1.5:
    resolution: {integrity: sha512-uN7m/BzVKQnCUF/iW8jYea67v++2u7m5UgENbHRtdDVclOUP+FMPlCNdmk0h/ysGyo2tavMJEDqJAkJdRa1vMA==}
    engines: {node: '>= 0.4'}

  functional-red-black-tree@1.0.1:
    resolution: {integrity: sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g==}

  functions-have-names@1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-intrinsic@1.2.0:
    resolution: {integrity: sha512-L049y6nFOuom5wGyRc3/gdTLO94dySVKRACj1RmJZBQXlbTMhtNIgkWkUHq+jYmZvKf14EW1EoJnnjbmoHij0Q==}

  get-stdin@8.0.0:
    resolution: {integrity: sha512-sY22aA6xchAzprjyqmSEQv4UbAAzRN0L2dQB0NlN5acTTK9Don6nhoc3eAbUnpZiCANAMfd/+40kVdKfFygohg==}
    engines: {node: '>=10'}

  get-stream@6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==}
    engines: {node: '>=10'}

  get-symbol-description@1.0.0:
    resolution: {integrity: sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw==}
    engines: {node: '>= 0.4'}

  get-uri@3.0.2:
    resolution: {integrity: sha512-+5s0SJbGoyiJTZZ2JTpFPLMPSch72KEqGOTvQsBqg0RBWvwhWUSYZFAtz3TPW0GXJuLBJPts1E241iHg+VRfhg==}
    engines: {node: '>= 6'}

  git-node-fs@1.0.0:
    resolution: {integrity: sha512-bLQypt14llVXBg0S0u8q8HmU7g9p3ysH+NvVlae5vILuUvs759665HvmR5+wb04KjHyjFcDRxdYb4kyNnluMUQ==}
    peerDependencies:
      js-git: ^0.7.8
    peerDependenciesMeta:
      js-git:
        optional: true

  git-sha1@0.1.2:
    resolution: {integrity: sha512-2e/nZezdVlyCopOCYHeW0onkbZg7xP1Ad6pndPy1rCygeRykefUS6r7oA5cJRGEFvseiaz5a/qUHFVX1dd6Isg==}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}

  glob-to-regexp@0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==}

  glob@7.1.6:
    resolution: {integrity: sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA==}

  glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}

  globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  globals@13.20.0:
    resolution: {integrity: sha512-Qg5QtVkCy/kv3FUSlu4ukeZDVf9ee0iXLAUYX13gbR17bnejFTzr4iS9bY7kwCf1NztRNm1t91fjOiyx4CSwPQ==}
    engines: {node: '>=8'}

  globalthis@1.0.3:
    resolution: {integrity: sha512-sFdI5LyBiNTHjRd7cGPWapiHWMOXKyuBNX/cWJ3NfzrZQVa8GI/8cofCl74AOVqq9W5kNmguTIzJ/1s2gyI9wA==}
    engines: {node: '>= 0.4'}

  globby@13.1.3:
    resolution: {integrity: sha512-8krCNHXvlCgHDpegPzleMq07yMYTO2sXKASmZmquEYWEmCx6J5UTRbp5RwMJkTJGtcQ44YpiUYUiN0b9mzy8Bw==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  gopd@1.0.1:
    resolution: {integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  grapheme-splitter@1.0.4:
    resolution: {integrity: sha512-bzh50DW9kTPM00T8y4o8vQg89Di9oLJVLW/KaOGIXJWP/iqCN6WKYkbNOF04vFLJhwcpYUh9ydh/+5vpOqV4YQ==}

  growly@1.3.0:
    resolution: {integrity: sha512-+xGQY0YyAWCnqy7Cd++hc2JqMYzlm0dG30Jd0beaA64sROr8C4nt8Yc9V5Ro3avlSUDTN0ulqP/VBKi1/lLygw==}

  gzip-size@6.0.0:
    resolution: {integrity: sha512-ax7ZYomf6jqPTQ4+XCpUGyXKHk5WweS+e05MBO4/y3WJ5RkmPXNKvX+bx1behVILVwr6JSQvZAku021CHPXG3Q==}
    engines: {node: '>=10'}

  handle-thing@2.0.1:
    resolution: {integrity: sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg==}

  has-ansi@2.0.0:
    resolution: {integrity: sha512-C8vBJ8DwUCx19vhm7urhTuUsr4/IyP6l4VzNQDv+ryHQObW3TTTp9yB68WpYgRe2bbaGuZ/se74IqFeVnMnLZg==}
    engines: {node: '>=0.10.0'}

  has-bigints@1.0.2:
    resolution: {integrity: sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==}

  has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.0:
    resolution: {integrity: sha512-62DVLZGoiEBDHQyqG4w9xCuZ7eJEwNmJRWw2VY84Oedb7WFcA27fiEVe8oUQx9hAUJ4ekurquucTGwsyO1XGdQ==}

  has-proto@1.0.1:
    resolution: {integrity: sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==}
    engines: {node: '>= 0.4'}

  has-symbols@1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.0:
    resolution: {integrity: sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==}
    engines: {node: '>= 0.4'}

  has@1.0.3:
    resolution: {integrity: sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==}
    engines: {node: '>= 0.4.0'}

  he@1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true

  hoist-non-react-statics@3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==}

  hpack.js@2.1.6:
    resolution: {integrity: sha512-zJxVehUdMGIKsRaNt7apO2Gqp0BdqW5yaiGHXXmbpvxgBYVZnAql+BJb4RO5ad2MgpbZKn5G6nMnegrH1FcNYQ==}

  html-entities@2.3.3:
    resolution: {integrity: sha512-DV5Ln36z34NNTDgnz0EWGBLZENelNAtkiFA4kyNOG2tDI6Mz1uSWiq1wAKdyjnJwyDiDO7Fa2SO1CTxPXL8VxA==}

  html-minifier-terser@6.1.0:
    resolution: {integrity: sha512-YXxSlJBZTP7RS3tWnQw74ooKa6L9b9i9QYXY21eUEvhZ3u9XLfv6OnFsQq6RxkhHygsaUMvYsZRV5rU/OVNZxw==}
    engines: {node: '>=12'}
    hasBin: true

  html-webpack-plugin@5.5.0:
    resolution: {integrity: sha512-sy88PC2cRTVxvETRgUHFrL4No3UxvcH8G1NepGhqaTT+GXN2kTamqasot0inS5hXeg1cMbFDt27zzo9p35lZVw==}
    engines: {node: '>=10.13.0'}
    peerDependencies:
      webpack: ^5.20.0

  htmlparser2@6.1.0:
    resolution: {integrity: sha512-gyyPk6rgonLFEDGoeRgQNaEUvdJ4ktTmmUh/h2t7s+M8oPpIPxgNACWa+6ESR57kXstwqPiCut0V8NRpcwgU7A==}

  http-assert@1.5.0:
    resolution: {integrity: sha512-uPpH7OKX4H25hBmU6G1jWNaqJGpTXxey+YOUizJUAgu0AjLUeC8D73hTrhvDS5D+GJN1DN1+hhc/eF/wpxtp0w==}
    engines: {node: '>= 0.8'}

  http-deceiver@1.2.7:
    resolution: {integrity: sha512-LmpOGxTfbpgtGVxJrj5k7asXHCgNZp5nLfp+hWc8QQRqtb7fUy6kRY3BO1h9ddF6yIPYUARgxGOwB42DnxIaNw==}

  http-errors@1.6.3:
    resolution: {integrity: sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A==}
    engines: {node: '>= 0.6'}

  http-errors@1.8.1:
    resolution: {integrity: sha512-Kpk9Sm7NmI+RHhnj6OIWDI1d6fIoFAtFt9RLaTMRlg/8w49juAStsrBgp0Dp4OdxdVbRIeKhtCUvoi/RuAhO4g==}
    engines: {node: '>= 0.6'}

  http-errors@2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}

  http-parser-js@0.5.8:
    resolution: {integrity: sha512-SGeBX54F94Wgu5RH3X5jsDtf4eHyRogWX1XGT3b4HuW3tQPM4AaBzoUji/4AAJNXCEOWZ5O0DgZmJw1947gD5Q==}

  http-proxy-agent@4.0.1:
    resolution: {integrity: sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg==}
    engines: {node: '>= 6'}

  http-proxy-middleware@2.0.6:
    resolution: {integrity: sha512-ya/UeJ6HVBYxrgYotAZo1KvPWlgB48kUJLDePFeneHsVujFaW5WNj2NgWCAE//B1Dl02BIfYlpNgBy8Kf8Rjmw==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@types/express': ^4.17.13
    peerDependenciesMeta:
      '@types/express':
        optional: true

  http-proxy@1.18.1:
    resolution: {integrity: sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==}
    engines: {node: '>=8.0.0'}

  https-proxy-agent@5.0.1:
    resolution: {integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==}
    engines: {node: '>= 6'}

  human-signals@2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==}
    engines: {node: '>=10.17.0'}

  iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}

  iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}

  icss-utils@5.1.0:
    resolution: {integrity: sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  ieee754@1.1.13:
    resolution: {integrity: sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg==}

  ignore-by-default@1.0.1:
    resolution: {integrity: sha512-Ius2VYcGNk7T90CppJqcIkS5ooHUZyIQK+ClZfMfMNFEF9VSE73Fq+906u/CWu92x4gzZMWOwfFYckPObzdEbA==}

  ignore-loader@0.1.2:
    resolution: {integrity: sha512-yOJQEKrNwoYqrWLS4DcnzM7SEQhRKis5mB+LdKKh4cPmGYlLPR0ozRzHV5jmEk2IxptqJNQA5Cc0gw8Fj12bXA==}

  ignore@4.0.6:
    resolution: {integrity: sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==}
    engines: {node: '>= 4'}

  ignore@5.2.4:
    resolution: {integrity: sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==}
    engines: {node: '>= 4'}

  image-size@0.5.5:
    resolution: {integrity: sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  import-fresh@3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}

  import-local@3.1.0:
    resolution: {integrity: sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg==}
    engines: {node: '>=8'}
    hasBin: true

  imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}

  inherits@2.0.3:
    resolution: {integrity: sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==}

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  ini@1.3.8:
    resolution: {integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==}

  internal-slot@1.0.5:
    resolution: {integrity: sha512-Y+R5hJrzs52QCG2laLn4udYVnxsfny9CpOhNhUvk/SSSVyF6T27FzRbF0sroPidSu3X8oEAkOn2K804mjpt6UQ==}
    engines: {node: '>= 0.4'}

  interpret@3.1.1:
    resolution: {integrity: sha512-6xwYfHbajpoF0xLW+iwLkhwgvLoZDfjYfoFNu8ftMoXINzwuymNLd9u/KmwtdT2GbR+/Cz66otEGEVVUHX9QLQ==}
    engines: {node: '>=10.13.0'}

  intl-messageformat@10.3.4:
    resolution: {integrity: sha512-/FxUIrlbPtuykSNX85CB5sp2FjLVeTmdD7TfRkVFPft2n4FgcSlAcilFytYiFAEmPHc+0PvpLCIPXeaGFzIvOg==}

  invariant@2.2.4:
    resolution: {integrity: sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==}

  ip@1.1.8:
    resolution: {integrity: sha512-PuExPYUiu6qMBQb4l06ecm6T6ujzhmh+MeJcW9wa89PoAz5pvd4zPgN5WJV104mb6S2T1AwNIAaB70JNrLQWhg==}

  ip@2.0.0:
    resolution: {integrity: sha512-WKa+XuLG1A1R0UWhl2+1XQSi+fZWMsYKffMZTTYsiZaUD8k2yDAj5atimTUD2TZkyCkNEeYE5NhFZmupOGtjYQ==}

  ipaddr.js@1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==}
    engines: {node: '>= 0.10'}

  ipaddr.js@2.0.1:
    resolution: {integrity: sha512-1qTgH9NG+IIJ4yfKs2e6Pp1bZg8wbDbKHT21HrLIeYBTRLgMYKnMTPAuI3Lcs61nfx5h1xlXnbJtH1kX5/d/ng==}
    engines: {node: '>= 10'}

  is-arguments@1.1.1:
    resolution: {integrity: sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==}
    engines: {node: '>= 0.4'}

  is-array-buffer@3.0.2:
    resolution: {integrity: sha512-y+FyyR/w8vfIRq4eQcM1EYgSTnmHXPqaF+IgzgraytCFq5Xh8lllDVmAZolPJiZttZLeFSINPYMaEJ7/vWUa1w==}

  is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}

  is-bigint@1.0.4:
    resolution: {integrity: sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==}

  is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}

  is-boolean-object@1.1.2:
    resolution: {integrity: sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==}
    engines: {node: '>= 0.4'}

  is-callable@1.2.7:
    resolution: {integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==}
    engines: {node: '>= 0.4'}

  is-core-module@2.11.0:
    resolution: {integrity: sha512-RRjxlvLDkD1YJwDbroBHMb+cukurkDWNyHx7D3oNB5x9rb5ogcksMC5wHCadcXoo67gVr/+3GFySh3134zi6rw==}

  is-date-object@1.0.5:
    resolution: {integrity: sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==}
    engines: {node: '>= 0.4'}

  is-docker@2.2.1:
    resolution: {integrity: sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==}
    engines: {node: '>=8'}
    hasBin: true

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@2.0.0:
    resolution: {integrity: sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w==}
    engines: {node: '>=4'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-generator-function@1.0.10:
    resolution: {integrity: sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==}
    engines: {node: '>= 0.4'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-negative-zero@2.0.2:
    resolution: {integrity: sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==}
    engines: {node: '>= 0.4'}

  is-number-object@1.0.7:
    resolution: {integrity: sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==}
    engines: {node: '>= 0.4'}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-path-inside@3.0.3:
    resolution: {integrity: sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==}
    engines: {node: '>=8'}

  is-plain-obj@3.0.0:
    resolution: {integrity: sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA==}
    engines: {node: '>=10'}

  is-plain-object@2.0.4:
    resolution: {integrity: sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==}
    engines: {node: '>=0.10.0'}

  is-promise@2.2.2:
    resolution: {integrity: sha512-+lP4/6lKUBfQjZ2pdxThZvLUAafmZb8OAxFb8XXtiQmS35INgr85hdOGoEs124ez1FCnZJt6jau/T+alh58QFQ==}

  is-regex@1.1.4:
    resolution: {integrity: sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==}
    engines: {node: '>= 0.4'}

  is-shared-array-buffer@1.0.2:
    resolution: {integrity: sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA==}

  is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}

  is-string@1.0.7:
    resolution: {integrity: sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==}
    engines: {node: '>= 0.4'}

  is-symbol@1.0.4:
    resolution: {integrity: sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==}
    engines: {node: '>= 0.4'}

  is-typed-array@1.1.10:
    resolution: {integrity: sha512-PJqgEHiWZvMpaFZ3uTc8kHPM4+4ADTlDniuQL7cU/UDA0Ql7F70yGfHph3cLNe+c9toaigv+DFzTJKhc2CtO6A==}
    engines: {node: '>= 0.4'}

  is-weakref@1.0.2:
    resolution: {integrity: sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==}

  is-what@3.14.1:
    resolution: {integrity: sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==}

  is-wsl@2.2.0:
    resolution: {integrity: sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==}
    engines: {node: '>=8'}

  isarray@0.0.1:
    resolution: {integrity: sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==}

  isarray@1.0.0:
    resolution: {integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  isobject@3.0.1:
    resolution: {integrity: sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==}
    engines: {node: '>=0.10.0'}

  jest-util@29.5.0:
    resolution: {integrity: sha512-RYMgG/MTadOr5t8KdhejfvUU82MxsCu5MF6KuDUHl+NuwzUt+Sm6jJWxTJVrDR1j5M/gJVCPKQEpWXY+yIQ6lQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-worker@27.5.1:
    resolution: {integrity: sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==}
    engines: {node: '>= 10.13.0'}

  jest-worker@29.5.0:
    resolution: {integrity: sha512-NcrQnevGoSp4b5kg+akIpthoAFHxPBcb5P6mYPY0fUNT+sSvmtu6jlkEle3anczUKIKEbMxFimk9oTP/tpIPgA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jiti@1.18.2:
    resolution: {integrity: sha512-QAdOptna2NYiSSpv0O/BwoHBSmz4YhpzJHyi+fnMRTXFjp7B8i/YG5Z8IfusxB1ufjcD2Sre1F3R+nX3fvy7gg==}
    hasBin: true

  jju@1.4.0:
    resolution: {integrity: sha512-8wb9Yw966OSxApiCt0K3yNJL8pnNeIv+OEq2YMidz4FKP6nonSRoOXc80iXY4JaN2FC11B9qsNmDsm+ZOfMROA==}

  jmespath@0.16.0:
    resolution: {integrity: sha512-9FzQjJ7MATs1tSpnco1K6ayiYE3figslrXA72G2HQ/n76RzvYlofyi5QM+iX4YRs/pu3yzxlVQSST23+dMDknw==}
    engines: {node: '>= 0.6.0'}

  js-git@0.7.8:
    resolution: {integrity: sha512-+E5ZH/HeRnoc/LW0AmAyhU+mNcWBzAKE+30+IDMLSLbbK+Tdt02AdkOKq9u15rlJsDEGFqtgckc8ZM59LhhiUA==}

  js-sdsl@4.4.0:
    resolution: {integrity: sha512-FfVSdx6pJ41Oa+CF7RDaFmTnCaFhua+SNYQX74riGOpl96x+2jQCqEfQ2bnXu/5DPCqlRuiqyvTJM0Qjz26IVg==}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-yaml@3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  jsesc@0.5.0:
    resolution: {integrity: sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA==}
    hasBin: true

  jsesc@2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true

  json-parse-better-errors@1.0.2:
    resolution: {integrity: sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==}

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}

  json-parse-helpfulerror@1.0.3:
    resolution: {integrity: sha512-XgP0FGR77+QhUxjXkwOMkC94k3WtqEBfcnjWqhRd82qTat4SWKRE+9kUnynz/shm3I4ea2+qISvTIeGTNU7kJg==}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}

  json-server@0.17.3:
    resolution: {integrity: sha512-LDNOvleTv3rPAcefzXZpXMDZshV0FtSzWo8ZjnTOhKm4OCiUvsYGrGrfz4iHXIFd+UbRgFHm6gcOHI/BSZ/3fw==}
    engines: {node: '>=12'}
    hasBin: true

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}

  json-stringify-safe@5.0.1:
    resolution: {integrity: sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==}

  json5@1.0.2:
    resolution: {integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==}
    hasBin: true

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  jsonfile@4.0.0:
    resolution: {integrity: sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==}

  jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}

  jsonschema@1.4.1:
    resolution: {integrity: sha512-S6cATIPVv1z0IlxdN+zUk5EPjkGCdnhN4wVSBlvoUO1tOLJootbo9CquNJmbIh4yikWHiUedhRYrNPn1arpEmQ==}

  jsx-ast-utils@3.3.3:
    resolution: {integrity: sha512-fYQHZTZ8jSfmWZ0iyzfwiU4WDX4HpHbMCZ3gPlWYiCl3BoeOTsqKBqnTVfH2rYT7eP5c3sVbeSPHnnJOaTrWiw==}
    engines: {node: '>=4.0'}

  keygrip@1.1.0:
    resolution: {integrity: sha512-iYSchDJ+liQ8iwbSI2QqsQOvqv58eJCEanyJPJi+Khyu8smkcKSFUCbPwzFcL7YVtZ6eONjqRX/38caJ7QjRAQ==}
    engines: {node: '>= 0.6'}

  kind-of@6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}

  klona@2.0.6:
    resolution: {integrity: sha512-dhG34DXATL5hSxJbIexCft8FChFXtmskoZYnoPWjXQuebWYCNkVeV3KkGegCK9CP1oswI/vQibS2GY7Em/sJJA==}
    engines: {node: '>= 8'}

  koa-compose@4.1.0:
    resolution: {integrity: sha512-8ODW8TrDuMYvXRwra/Kh7/rJo9BtOfPc6qO8eAfC80CnCvSjSl0bkRM24X6/XBBEyj0v1nRUQ1LyOy3dbqOWXw==}

  koa-convert@2.0.0:
    resolution: {integrity: sha512-asOvN6bFlSnxewce2e/DK3p4tltyfC4VM7ZwuTuepI7dEQVcvpyFuBcEARu1+Hxg8DIwytce2n7jrZtRlPrARA==}
    engines: {node: '>= 10'}

  koa-create-context@1.0.2:
    resolution: {integrity: sha512-8ol+pprFO10qAzns42GE0QayVfa9ZiNNsExTidyLyl0Ar2VZ6wnVw5Ts0wR1onSkjwgf3u9m1INEydfdpY6fsw==}
    peerDependencies:
      '@types/koa': ^2.0.48
      koa: ^2.7.0

  koa-mount@4.0.0:
    resolution: {integrity: sha512-rm71jaA/P+6HeCpoRhmCv8KVBIi0tfGuO/dMKicbQnQW/YJntJ6MnnspkodoA4QstMVEZArsCphmd0bJEtoMjQ==}
    engines: {node: '>= 7.6.0'}

  koa-send@5.0.1:
    resolution: {integrity: sha512-tmcyQ/wXXuxpDxyNXv5yNNkdAMdFRqwtegBXUaowiQzUKqJehttS0x2j0eOZDQAyloAth5w6wwBImnFzkUz3pQ==}
    engines: {node: '>= 8'}

  koa-static@5.0.0:
    resolution: {integrity: sha512-UqyYyH5YEXaJrf9S8E23GoJFQZXkBVJ9zYYMPGz919MSX1KuvAcycIuS0ci150HCoPf4XQVhQ84Qf8xRPWxFaQ==}
    engines: {node: '>= 7.6.0'}

  koa@2.14.1:
    resolution: {integrity: sha512-USJFyZgi2l0wDgqkfD27gL4YGno7TfUkcmOe6UOLFOVuN+J7FwnNu4Dydl4CUQzraM1lBAiGed0M9OVJoT0Kqw==}
    engines: {node: ^4.8.4 || ^6.10.1 || ^7.10.1 || >= 8.1.4}

  launch-editor@2.6.0:
    resolution: {integrity: sha512-JpDCcQnyAAzZZaZ7vEiSqL690w7dAEyLao+KC96zBplnYbJS7TYNjvM3M7y3dGz+v7aIsJk3hllWuc0kWAjyRQ==}

  lazy@1.0.11:
    resolution: {integrity: sha512-Y+CjUfLmIpoUCCRl0ub4smrYtGGr5AOa2AKOaWelGHOGz33X/Y/KizefGqbkwfz44+cnq/+9habclf8vOmu2LA==}
    engines: {node: '>=0.2.0'}

  less-loader@11.1.0:
    resolution: {integrity: sha512-C+uDBV7kS7W5fJlUjq5mPBeBVhYpTIm5gB09APT9o3n/ILeaXVsiSFTbZpTJCJwQ/Crczfn3DmfQFwxYusWFug==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      less: ^3.5.0 || ^4.0.0
      webpack: ^5.0.0

  less@4.1.3:
    resolution: {integrity: sha512-w16Xk/Ta9Hhyei0Gpz9m7VS8F28nieJaL/VyShID7cYvP6IL5oHeL6p4TXSDJqZE/lNv0oJ2pGVjJsRkfwm5FA==}
    engines: {node: '>=6'}
    hasBin: true

  levn@0.3.0:
    resolution: {integrity: sha512-0OO4y2iOHix2W6ujICbKIaEQXvFQHue65vUG3pb5EUomzPI90z9hsA1VsO/dbIIpC53J8gxM9Q4Oho0jrCM/yA==}
    engines: {node: '>= 0.8.0'}

  levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}

  lilconfig@2.1.0:
    resolution: {integrity: sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==}
    engines: {node: '>=10'}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}

  load-json-file@5.3.0:
    resolution: {integrity: sha512-cJGP40Jc/VXUsp8/OrnyKyTZ1y6v/dphm3bioS+RrKXjK2BB6wHUd6JptZEFDGgGahMT+InnZO5i1Ei9mpC8Bw==}
    engines: {node: '>=6'}

  loader-runner@4.3.0:
    resolution: {integrity: sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==}
    engines: {node: '>=6.11.5'}

  loader-utils@2.0.4:
    resolution: {integrity: sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==}
    engines: {node: '>=8.9.0'}

  locate-path@3.0.0:
    resolution: {integrity: sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==}
    engines: {node: '>=6'}

  locate-path@5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}

  locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}

  lodash-id@0.14.1:
    resolution: {integrity: sha512-ikQPBTiq/d5m6dfKQlFdIXFzvThPi2Be9/AHxktOnDSfSxE1j9ICbBT5Elk1ke7HSTgM38LHTpmJovo9/klnLg==}
    engines: {node: '>= 4'}

  lodash.debounce@4.0.8:
    resolution: {integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==}

  lodash.memoize@4.1.2:
    resolution: {integrity: sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==}

  lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}

  lodash.truncate@4.4.2:
    resolution: {integrity: sha512-jttmRe7bRse52OsWIMDLaXxWqRAmtIUccAQ3garviCqJjafXOfNMO0yMfNpdD6zbGaTU0P5Nz7e7gAT6cKmJRw==}

  lodash.uniq@4.5.0:
    resolution: {integrity: sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  log-driver@1.2.7:
    resolution: {integrity: sha512-U7KCmLdqsGHBLeWqYlFA0V0Sl6P08EE1ZrmA9cxjUE0WVqT9qnyVDPz1kzpFEP0jdJuFnasWIfSd7fsaNXkpbg==}
    engines: {node: '>=0.8.6'}

  log-update@2.3.0:
    resolution: {integrity: sha512-vlP11XfFGyeNQlmEn9tJ66rEW1coA/79m5z6BCkudjbAGE83uhAcGYrBFwfs3AdLiLzGRusRPAbSPK9xZteCmg==}
    engines: {node: '>=4'}

  loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true

  lowdb@1.0.0:
    resolution: {integrity: sha512-2+x8esE/Wb9SQ1F9IHaYWfsC9FIecLOPrK4g17FGEayjUWH172H6nwicRovGvSE2CPZouc2MCIqCI7h9d+GftQ==}
    engines: {node: '>=4'}

  lower-case@2.0.2:
    resolution: {integrity: sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  lru-cache@6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}

  make-dir@2.1.0:
    resolution: {integrity: sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==}
    engines: {node: '>=6'}

  make-dir@3.1.0:
    resolution: {integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==}
    engines: {node: '>=8'}

  make-error@1.3.6:
    resolution: {integrity: sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==}

  mdn-data@2.0.14:
    resolution: {integrity: sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==}

  media-typer@0.3.0:
    resolution: {integrity: sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==}
    engines: {node: '>= 0.6'}

  memfs@3.5.0:
    resolution: {integrity: sha512-yK6o8xVJlQerz57kvPROwTMgx5WtGwC2ZxDtOUsnGl49rHjYkfQoPNZPCKH73VdLE1BwBu/+Fx/NL8NYMUw2aA==}
    engines: {node: '>= 4.0.0'}

  merge-descriptors@1.0.1:
    resolution: {integrity: sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w==}

  merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}

  merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  method-override@3.0.0:
    resolution: {integrity: sha512-IJ2NNN/mSl9w3kzWB92rcdHpz+HjkxhDJWNDBqSlas+zQdP8wBiJzITPg08M/k2uVvMow7Sk41atndNtt/PHSA==}
    engines: {node: '>= 0.10'}

  methods@1.1.2:
    resolution: {integrity: sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==}
    engines: {node: '>= 0.6'}

  micromatch@4.0.5:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true

  mimic-fn@1.2.0:
    resolution: {integrity: sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ==}
    engines: {node: '>=4'}

  mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}

  mini-css-extract-plugin@2.7.5:
    resolution: {integrity: sha512-9HaR++0mlgom81s95vvNjxkg52n2b5s//3ZTI1EtzFb98awsLSivs2LMsVqnQ3ay0PVhqWcGNyDaTE961FOcjQ==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^5.0.0

  minimalistic-assert@1.0.1:
    resolution: {integrity: sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==}

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  mkdirp@1.0.4:
    resolution: {integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==}
    engines: {node: '>=10'}
    hasBin: true

  mock-req@0.2.0:
    resolution: {integrity: sha512-IUuwS0W5GjoPyjhuXPQJXpaHfHW7UYFRia8Cchm/xRuyDDclpSQdEoakt3krOpSYvgVlQsbnf0ePDsTRDfp7Dg==}

  mock-res@0.5.0:
    resolution: {integrity: sha512-KBTRmramDPSr1TwsTy54uzm50QUAdyLTy6CXcHwwPN9pMVH90S12rwbAvXf3Bghr2TNPEuLmz227ClR8SIwQNQ==}

  module-details-from-path@1.0.3:
    resolution: {integrity: sha512-ySViT69/76t8VhE1xXHK6Ch4NcDd26gx0MzKXLO+F7NOtnqH68d9zF94nT8ZWSxXh8ELOERsnJO/sWt1xZYw5A==}

  morgan@1.10.0:
    resolution: {integrity: sha512-AbegBVI4sh6El+1gNwvD5YIck7nSA36weD7xvIxG4in80j/UoK8AEGaWnnz8v1GxonMCltmlNs5ZKbGvl9b1XQ==}
    engines: {node: '>= 0.8.0'}

  mrmime@1.0.1:
    resolution: {integrity: sha512-hzzEagAgDyoU1Q6yg5uI+AorQgdvMCur3FcKf7NhMKWsaYg+RnbTyHRa/9IlLF9rf455MOCtcqqrQQ83pPP7Uw==}
    engines: {node: '>=10'}

  ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}

  ms@2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  multicast-dns@7.2.5:
    resolution: {integrity: sha512-2eznPJP8z2BFLX50tf0LuODrpINqP1RVIm/CObbTcBRITQgmC/TjcREF1NeTBzIcR5XO/ukWo+YHOjBbFwIupg==}
    hasBin: true

  mute-stream@0.0.8:
    resolution: {integrity: sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==}

  mz@2.7.0:
    resolution: {integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==}

  nanoid@3.3.6:
    resolution: {integrity: sha512-BGcqMMJuToF7i1rt+2PWSNVnWIkGCU78jBG3RxO/bZlnZPK2Cmi2QaffxGO/2RvWi9sL+FAiRiXMgsyxQ1DIDA==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  nanoid@3.3.7:
    resolution: {integrity: sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}

  needle@2.4.0:
    resolution: {integrity: sha512-4Hnwzr3mi5L97hMYeNl8wRW/Onhy4nUKR/lVemJ8gJedxxUyBLm9kkrDColJvoSfwi0jCNhD+xCdOtiGDQiRZg==}
    engines: {node: '>= 4.4.x'}
    hasBin: true

  needle@3.3.1:
    resolution: {integrity: sha512-6k0YULvhpw+RoLNiQCRKOl09Rv1dPLr8hHnVjHqdolKwDrdNyk+Hmrthi4lIGPPz3r39dLx0hsF5s40sZ3Us4Q==}
    engines: {node: '>= 4.4.x'}
    hasBin: true

  negotiator@0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}

  neo-async@2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==}

  netmask@2.0.2:
    resolution: {integrity: sha512-dBpDMdxv9Irdq66304OLfEmQ9tbNRFnFTuZiLo+bD+r332bBmMJ8GBLXklIXXgxd3+v9+KUnZaUR5PJMa75Gsg==}
    engines: {node: '>= 0.4.0'}

  no-case@3.0.4:
    resolution: {integrity: sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==}

  node-abort-controller@3.1.1:
    resolution: {integrity: sha512-AGK2yQKIjRuqnc6VkX2Xj5d+QW8xZ87pa1UK6yA6ouUyuxfHuMP6umE5QK7UmTeOAymo+Zx1Fxiuw9rVx8taHQ==}

  node-forge@1.3.1:
    resolution: {integrity: sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA==}
    engines: {node: '>= 6.13.0'}

  node-notifier@9.0.1:
    resolution: {integrity: sha512-fPNFIp2hF/Dq7qLDzSg4vZ0J4e9v60gJR+Qx7RbjbWqzPDdEqeVpEx5CFeDAELIl+A/woaaNn1fQ5nEVerMxJg==}

  node-releases@2.0.10:
    resolution: {integrity: sha512-5GFldHPXVG/YZmFzJvKK2zDSzPKhEp0+ZR5SVaoSag9fsL5YgHbUHDfnG5494ISANDcK4KwPXAx2xqVEydmd7w==}

  nodemon@3.1.0:
    resolution: {integrity: sha512-xqlktYlDMCepBJd43ZQhjWwMw2obW/JRvkrLxq5RCNcuDDX1DbcPT+qT1IlIIdf+DhnWs90JpTMe+Y5KxOchvA==}
    engines: {node: '>=10'}
    hasBin: true

  nopt@1.0.10:
    resolution: {integrity: sha512-NWmpvLSqUrgrAC9HCuxEvb+PSloHpqVu+FqcO4eeF2h5qYRhA7ev6KvelyQAKtegUbC6RypJnlEOhd8vloNKYg==}
    hasBin: true

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  normalize-range@0.1.2:
    resolution: {integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==}
    engines: {node: '>=0.10.0'}

  normalize-url@6.1.0:
    resolution: {integrity: sha512-DlL+XwOy3NxAQ8xuC0okPgK46iuVNAK01YN7RueYBqqFeGsBjV9XmCAzAdgt+667bCl5kPh9EqKKDwnaPG1I7A==}
    engines: {node: '>=10'}

  npm-run-path@4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}

  nssocket@0.6.0:
    resolution: {integrity: sha512-a9GSOIql5IqgWJR3F/JXG4KpJTA3Z53Cj0MeMvGpglytB1nxE4PdFNC0jINe27CS7cGivoynwc054EzCcT3M3w==}
    engines: {node: '>= 0.10.x'}

  nth-check@2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}

  null-loader@4.0.1:
    resolution: {integrity: sha512-pxqVbi4U6N26lq+LmgIbB5XATP0VdZKOG25DhHi8btMmJJefGArFyDg1yc4U3hWCJbMqSrw0qyrz1UQX+qYXqg==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  object-hash@3.0.0:
    resolution: {integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==}
    engines: {node: '>= 6'}

  object-inspect@1.12.3:
    resolution: {integrity: sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g==}

  object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}

  object.assign@4.1.4:
    resolution: {integrity: sha512-1mxKf0e58bvyjSCtKYY4sRe9itRk3PJpquJOjeIkz885CczcI4IvJJDLPS72oowuSh+pBxUFROpX+TU++hxhZQ==}
    engines: {node: '>= 0.4'}

  object.entries@1.1.6:
    resolution: {integrity: sha512-leTPzo4Zvg3pmbQ3rDK69Rl8GQvIqMWubrkxONG9/ojtFE2rD9fjMKfSI5BxW3osRH1m6VdzmqK8oAY9aT4x5w==}
    engines: {node: '>= 0.4'}

  object.fromentries@2.0.6:
    resolution: {integrity: sha512-VciD13dswC4j1Xt5394WR4MzmAQmlgN72phd/riNp9vtD7tp4QQWJ0R4wvclXcafgcYK8veHRed2W6XeGBvcfg==}
    engines: {node: '>= 0.4'}

  object.hasown@1.1.2:
    resolution: {integrity: sha512-B5UIT3J1W+WuWIU55h0mjlwaqxiE5vYENJXIXZ4VFe05pNYrkKuK0U/6aFcb0pKywYJh7IhfoqUfKVmrJJHZHw==}

  object.values@1.1.6:
    resolution: {integrity: sha512-FVVTkD1vENCsAcwNs9k6jea2uHC/X0+JcjG8YA60FN5CMaJmG95wT9jek/xX9nornqGRrBkKtzuAu2wuHpKqvw==}
    engines: {node: '>= 0.4'}

  obuf@1.1.2:
    resolution: {integrity: sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==}

  on-finished@2.3.0:
    resolution: {integrity: sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww==}
    engines: {node: '>= 0.8'}

  on-finished@2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}

  on-headers@1.0.2:
    resolution: {integrity: sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==}
    engines: {node: '>= 0.8'}

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  onetime@2.0.1:
    resolution: {integrity: sha512-oyyPpiMaKARvvcgip+JV+7zci5L8D1W9RZIz2l1o08AM3pfspitVWnPt3mzHcBPp12oYMTy0pqrFs/C+m3EwsQ==}
    engines: {node: '>=4'}

  onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}

  only@0.0.2:
    resolution: {integrity: sha512-Fvw+Jemq5fjjyWz6CpKx6w9s7xxqo3+JCyM0WXWeCSOboZ8ABkyvP8ID4CZuChA/wxSx+XSJmdOm8rGVyJ1hdQ==}

  open@8.4.2:
    resolution: {integrity: sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ==}
    engines: {node: '>=12'}

  opener@1.5.2:
    resolution: {integrity: sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A==}
    hasBin: true

  optionator@0.8.3:
    resolution: {integrity: sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==}
    engines: {node: '>= 0.8.0'}

  optionator@0.9.1:
    resolution: {integrity: sha512-74RlY5FCnhq4jRxVUPKDaRwrVNXMqsGsiW6AJw4XK8hmtm10wC0ypZBLw5IIp85NZMr91+qd1RvvENwg7jjRFw==}
    engines: {node: '>= 0.8.0'}

  p-limit@2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}

  p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}

  p-locate@3.0.0:
    resolution: {integrity: sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==}
    engines: {node: '>=6'}

  p-locate@4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}

  p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}

  p-retry@4.6.2:
    resolution: {integrity: sha512-312Id396EbJdvRONlngUx0NydfrIQ5lsYu0znKVUzVvArzEIt08V1qhtyESbGVd1FGX7UKtiFp5uwKZdM8wIuQ==}
    engines: {node: '>=8'}

  p-try@2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}

  pac-proxy-agent@5.0.0:
    resolution: {integrity: sha512-CcFG3ZtnxO8McDigozwE3AqAw15zDvGH+OjXO4kzf7IkEKkQ4gxQ+3sdF50WmhQ4P/bVusXcqNE2S3XrNURwzQ==}
    engines: {node: '>= 8'}

  pac-resolver@5.0.1:
    resolution: {integrity: sha512-cy7u00ko2KVgBAjuhevqpPeHIkCIqPe1v24cydhWjmeuzaBfmUWFCZJ1iAh5TuVzVZoUzXIW7K8sMYOZ84uZ9Q==}
    engines: {node: '>= 8'}

  pako@0.2.9:
    resolution: {integrity: sha512-NUcwaKxUxWrZLpDG+z/xZaCgQITkA/Dv4V/T6bw7VON6l1Xz/VnrBqrYjZQ12TamKHzITTfOEIYUj48y2KXImA==}

  param-case@3.0.4:
    resolution: {integrity: sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A==}

  parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}

  parse-json@4.0.0:
    resolution: {integrity: sha512-aOIos8bujGN93/8Ox/jPLh7RwVnPEysynVFE+fQZyg6jKELEHwzgKdLRFHUgXJL6kylijVSBC4BvN9OmsB48Rw==}
    engines: {node: '>=4'}

  parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}

  parse-node-version@1.0.1:
    resolution: {integrity: sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==}
    engines: {node: '>= 0.10'}

  parseurl@1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}

  pascal-case@3.1.2:
    resolution: {integrity: sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==}

  path-exists@3.0.0:
    resolution: {integrity: sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ==}
    engines: {node: '>=4'}

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  path-to-regexp@0.1.7:
    resolution: {integrity: sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ==}

  path-to-regexp@1.8.0:
    resolution: {integrity: sha512-n43JRhlUKUAlibEJhPeir1ncUID16QnEjNpwzNdO3Lm4ywrBpBZ5oLD0I6br9evr1Y9JTqwRtAh7JLoOzAQdVA==}

  path-to-regexp@6.2.1:
    resolution: {integrity: sha512-JLyh7xT1kizaEvcaXOQwOc2/Yhw6KZOvPf1S8401UyLk86CU79LN3vl7ztXGm/pZ+YjoyAJ4rxmHwbkBXJX+yw==}

  path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}

  picocolors@1.0.0:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  pidusage@2.0.21:
    resolution: {integrity: sha512-cv3xAQos+pugVX+BfXpHsbyz/dLzX+lr44zNMsYiGxUw+kV5sgQCIcLd1z+0vq+KyC7dJ+/ts2PsfgWfSC3WXA==}
    engines: {node: '>=8'}

  pidusage@3.0.2:
    resolution: {integrity: sha512-g0VU+y08pKw5M8EZ2rIGiEBaB8wrQMjYGFfW2QVIfyT8V+fq8YFLkvlz4bz5ljvFDJYNFCWT3PWqcRr2FKO81w==}
    engines: {node: '>=10'}

  pify@2.3.0:
    resolution: {integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==}
    engines: {node: '>=0.10.0'}

  pify@3.0.0:
    resolution: {integrity: sha512-C3FsVNH1udSEX48gGX1xfvwTWfsYWj5U+8/uK15BGzIGrKoUpghX8hWZwa/OFnakBiiVNmBvemTJR5mcy7iPcg==}
    engines: {node: '>=4'}

  pify@4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==}
    engines: {node: '>=6'}

  pirates@4.0.5:
    resolution: {integrity: sha512-8V9+HQPupnaXMA23c5hvl69zXvTwTzyAYasnkb0Tts4XvO4CliqONMOnvlq26rkhLC3nWDFBJf73LU1e1VZLaQ==}
    engines: {node: '>= 6'}

  pkg-conf@3.1.0:
    resolution: {integrity: sha512-m0OTbR/5VPNPqO1ph6Fqbj7Hv6QU7gR/tQW40ZqrL1rjgCU85W6C1bJn0BItuJqnR98PWzw7Z8hHeChD1WrgdQ==}
    engines: {node: '>=6'}

  pkg-dir@4.2.0:
    resolution: {integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==}
    engines: {node: '>=8'}

  please-upgrade-node@3.2.0:
    resolution: {integrity: sha512-gQR3WpIgNIKwBMVLkpMUeR3e1/E1y42bqDQZfql+kDeXd8COYfM8PQA4X6y7a8u9Ua9FHmsrrmirW2vHs45hWg==}

  pluralize@8.0.0:
    resolution: {integrity: sha512-Nc3IT5yHzflTfbjgqWcCPpo7DaKy4FnpB0l/zCAW0Tc7jxAiuqSxHasntB3D7887LSrA93kDJ9IXovxJYxyLCA==}
    engines: {node: '>=4'}

  pm2-axon-rpc@0.7.1:
    resolution: {integrity: sha512-FbLvW60w+vEyvMjP/xom2UPhUN/2bVpdtLfKJeYM3gwzYhoTEEChCOICfFzxkxuoEleOlnpjie+n1nue91bDQw==}
    engines: {node: '>=5'}

  pm2-axon@4.0.1:
    resolution: {integrity: sha512-kES/PeSLS8orT8dR5jMlNl+Yu4Ty3nbvZRmaAtROuVm9nYYGiaoXqqKQqQYzWQzMYWUKHMQTvBlirjE5GIIxqg==}
    engines: {node: '>=5'}

  pm2-deploy@1.0.2:
    resolution: {integrity: sha512-YJx6RXKrVrWaphEYf++EdOOx9EH18vM8RSZN/P1Y+NokTKqYAca/ejXwVLyiEpNju4HPZEk3Y2uZouwMqUlcgg==}
    engines: {node: '>=4.0.0'}

  pm2-multimeter@0.1.2:
    resolution: {integrity: sha512-S+wT6XfyKfd7SJIBqRgOctGxaBzUOmVQzTAS+cg04TsEUObJVreha7lvCfX8zzGVr871XwCSnHUU7DQQ5xEsfA==}

  pm2-sysmonit@1.2.8:
    resolution: {integrity: sha512-ACOhlONEXdCTVwKieBIQLSi2tQZ8eKinhcr9JpZSUAL8Qy0ajIgRtsLxG/lwPOW3JEKqPyw/UaHmTWhUzpP4kA==}

  pm2@5.3.0:
    resolution: {integrity: sha512-xscmQiAAf6ArVmKhjKTeeN8+Td7ZKnuZFFPw1DGkdFPR/0Iyx+m+1+OpCdf9+HQopX3VPc9/wqPQHqVOfHum9w==}
    engines: {node: '>=10.0.0'}
    hasBin: true

  postcss-attribute-case-insensitive@5.0.2:
    resolution: {integrity: sha512-XIidXV8fDr0kKt28vqki84fRK8VW8eTuIa4PChv2MqKuT6C9UjmSKzen6KaWhWEoYvwxFCa7n/tC1SZ3tyq4SQ==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-calc@8.2.4:
    resolution: {integrity: sha512-SmWMSJmB8MRnnULldx0lQIyhSNvuDl9HfrZkaqqE/WHAhToYsAvDq+yAsA/kIyINDszOp3Rh0GFoNuH5Ypsm3Q==}
    peerDependencies:
      postcss: ^8.2.2

  postcss-clamp@4.1.0:
    resolution: {integrity: sha512-ry4b1Llo/9zz+PKC+030KUnPITTJAHeOwjfAyyB60eT0AorGLdzp52s31OsPRHRf8NchkgFoG2y6fCfn1IV1Ow==}
    engines: {node: '>=7.6.0'}
    peerDependencies:
      postcss: ^8.4.6

  postcss-color-functional-notation@4.2.4:
    resolution: {integrity: sha512-2yrTAUZUab9s6CpxkxC4rVgFEVaR6/2Pipvi6qcgvnYiVqZcbDHEoBDhrXzyb7Efh2CCfHQNtcqWcIruDTIUeg==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-color-hex-alpha@8.0.4:
    resolution: {integrity: sha512-nLo2DCRC9eE4w2JmuKgVA3fGL3d01kGq752pVALF68qpGLmx2Qrk91QTKkdUqqp45T1K1XV8IhQpcu1hoAQflQ==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.4

  postcss-color-rebeccapurple@7.1.1:
    resolution: {integrity: sha512-pGxkuVEInwLHgkNxUc4sdg4g3py7zUeCQ9sMfwyHAT+Ezk8a4OaaVZ8lIY5+oNqA/BXXgLyXv0+5wHP68R79hg==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-colormin@5.3.1:
    resolution: {integrity: sha512-UsWQG0AqTFQmpBegeLLc1+c3jIqBNB0zlDGRWR+dQ3pRKJL1oeMzyqmH3o2PIfn9MBdNrVPWhDbT769LxCTLJQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-convert-values@5.1.3:
    resolution: {integrity: sha512-82pC1xkJZtcJEfiLw6UXnXVXScgtBrjlO5CBmuDQc+dlb88ZYheFsjTn40+zBVi3DkfF7iezO0nJUPLcJK3pvA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-custom-media@8.0.2:
    resolution: {integrity: sha512-7yi25vDAoHAkbhAzX9dHx2yc6ntS4jQvejrNcC+csQJAXjj15e7VcWfMgLqBNAbOvqi5uIa9huOVwdHbf+sKqg==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.3

  postcss-custom-properties@12.1.11:
    resolution: {integrity: sha512-0IDJYhgU8xDv1KY6+VgUwuQkVtmYzRwu+dMjnmdMafXYv86SWqfxkc7qdDvWS38vsjaEtv8e0vGOUQrAiMBLpQ==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-custom-selectors@6.0.3:
    resolution: {integrity: sha512-fgVkmyiWDwmD3JbpCmB45SvvlCD6z9CG6Ie6Iere22W5aHea6oWa7EM2bpnv2Fj3I94L3VbtvX9KqwSi5aFzSg==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.3

  postcss-dir-pseudo-class@6.0.5:
    resolution: {integrity: sha512-eqn4m70P031PF7ZQIvSgy9RSJ5uI2171O/OO/zcRNYpJbvaeKFUlar1aJ7rmgiQtbm0FSPsRewjpdS0Oew7MPA==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-discard-comments@5.1.2:
    resolution: {integrity: sha512-+L8208OVbHVF2UQf1iDmRcbdjJkuBF6IS29yBDSiWUIzpYaAhtNl6JYnYm12FnkeCwQqF5LeklOu6rAqgfBZqQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-discard-duplicates@5.1.0:
    resolution: {integrity: sha512-zmX3IoSI2aoenxHV6C7plngHWWhUOV3sP1T8y2ifzxzbtnuhk1EdPwm0S1bIUNaJ2eNbWeGLEwzw8huPD67aQw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-discard-empty@5.1.1:
    resolution: {integrity: sha512-zPz4WljiSuLWsI0ir4Mcnr4qQQ5e1Ukc3i7UfE2XcrwKK2LIPIqE5jxMRxO6GbI3cv//ztXDsXwEWT3BHOGh3A==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-discard-overridden@5.1.0:
    resolution: {integrity: sha512-21nOL7RqWR1kasIVdKs8HNqQJhFxLsyRfAnUDm4Fe4t4mCWL9OJiHvlHPjcd8zc5Myu89b/7wZDnOSjFgeWRtw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-double-position-gradients@3.1.2:
    resolution: {integrity: sha512-GX+FuE/uBR6eskOK+4vkXgT6pDkexLokPaz/AbJna9s5Kzp/yl488pKPjhy0obB475ovfT1Wv8ho7U/cHNaRgQ==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-env-function@4.0.6:
    resolution: {integrity: sha512-kpA6FsLra+NqcFnL81TnsU+Z7orGtDTxcOhl6pwXeEq1yFPpRMkCDpHhrz8CFQDr/Wfm0jLiNQ1OsGGPjlqPwA==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.4

  postcss-focus-visible@6.0.4:
    resolution: {integrity: sha512-QcKuUU/dgNsstIK6HELFRT5Y3lbrMLEOwG+A4s5cA+fx3A3y/JTq3X9LaOj3OC3ALH0XqyrgQIgey/MIZ8Wczw==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.4

  postcss-focus-within@5.0.4:
    resolution: {integrity: sha512-vvjDN++C0mu8jz4af5d52CB184ogg/sSxAFS+oUJQq2SuCe7T5U2iIsVJtsCp2d6R4j0jr5+q3rPkBVZkXD9fQ==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.4

  postcss-font-variant@5.0.0:
    resolution: {integrity: sha512-1fmkBaCALD72CK2a9i468mA/+tr9/1cBxRRMXOUaZqO43oWPR5imcyPjXwuv7PXbCid4ndlP5zWhidQVVa3hmA==}
    peerDependencies:
      postcss: ^8.1.0

  postcss-gap-properties@3.0.5:
    resolution: {integrity: sha512-IuE6gKSdoUNcvkGIqdtjtcMtZIFyXZhmFd5RUlg97iVEvp1BZKV5ngsAjCjrVy+14uhGBQl9tzmi1Qwq4kqVOg==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-image-set-function@4.0.7:
    resolution: {integrity: sha512-9T2r9rsvYzm5ndsBE8WgtrMlIT7VbtTfE7b3BQnudUqnBcBo7L758oc+o+pdj/dUV0l5wjwSdjeOH2DZtfv8qw==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-import@14.1.0:
    resolution: {integrity: sha512-flwI+Vgm4SElObFVPpTIT7SU7R3qk2L7PyduMcokiaVKuWv9d/U+Gm/QAd8NDLuykTWTkcrjOeD2Pp1rMeBTGw==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      postcss: ^8.0.0

  postcss-initial@4.0.1:
    resolution: {integrity: sha512-0ueD7rPqX8Pn1xJIjay0AZeIuDoF+V+VvMt/uOnn+4ezUKhZM/NokDeP6DwMNyIoYByuN/94IQnt5FEkaN59xQ==}
    peerDependencies:
      postcss: ^8.0.0

  postcss-js@4.0.1:
    resolution: {integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==}
    engines: {node: ^12 || ^14 || >= 16}
    peerDependencies:
      postcss: ^8.4.21

  postcss-lab-function@4.2.1:
    resolution: {integrity: sha512-xuXll4isR03CrQsmxyz92LJB2xX9n+pZJ5jE9JgcnmsCammLyKdlzrBin+25dy6wIjfhJpKBAN80gsTlCgRk2w==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-load-config@3.1.4:
    resolution: {integrity: sha512-6DiM4E7v4coTE4uzA8U//WhtPwyhiim3eyjEMFCnUpzbrkK9wJHgKDT2mR+HbtSrd/NubVaYTOpSpjUl8NQeRg==}
    engines: {node: '>= 10'}
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true

  postcss-loader@7.2.4:
    resolution: {integrity: sha512-F88rpxxNspo5hatIc+orYwZDtHFaVFOSIVAx+fBfJC1GmhWbVmPWtmg2gXKE1OxJbneOSGn8PWdIwsZFcruS+w==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      postcss: ^7.0.0 || ^8.0.1
      ts-node: '>=10'
      typescript: '>=4'
      webpack: ^5.0.0
    peerDependenciesMeta:
      ts-node:
        optional: true
      typescript:
        optional: true

  postcss-logical@5.0.4:
    resolution: {integrity: sha512-RHXxplCeLh9VjinvMrZONq7im4wjWGlRJAqmAVLXyZaXwfDWP73/oq4NdIp+OZwhQUMj0zjqDfM5Fj7qby+B4g==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.4

  postcss-media-minmax@5.0.0:
    resolution: {integrity: sha512-yDUvFf9QdFZTuCUg0g0uNSHVlJ5X1lSzDZjPSFaiCWvjgsvu8vEVxtahPrLMinIDEEGnx6cBe6iqdx5YWz08wQ==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      postcss: ^8.1.0

  postcss-merge-longhand@5.1.7:
    resolution: {integrity: sha512-YCI9gZB+PLNskrK0BB3/2OzPnGhPkBEwmwhfYk1ilBHYVAZB7/tkTHFBAnCrvBBOmeYyMYw3DMjT55SyxMBzjQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-merge-rules@5.1.4:
    resolution: {integrity: sha512-0R2IuYpgU93y9lhVbO/OylTtKMVcHb67zjWIfCiKR9rWL3GUk1677LAqD/BcHizukdZEjT8Ru3oHRoAYoJy44g==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-minify-font-values@5.1.0:
    resolution: {integrity: sha512-el3mYTgx13ZAPPirSVsHqFzl+BBBDrXvbySvPGFnQcTI4iNslrPaFq4muTkLZmKlGk4gyFAYUBMH30+HurREyA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-minify-gradients@5.1.1:
    resolution: {integrity: sha512-VGvXMTpCEo4qHTNSa9A0a3D+dxGFZCYwR6Jokk+/3oB6flu2/PnPXAh2x7x52EkY5xlIHLm+Le8tJxe/7TNhzw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-minify-params@5.1.4:
    resolution: {integrity: sha512-+mePA3MgdmVmv6g+30rn57USjOGSAyuxUmkfiWpzalZ8aiBkdPYjXWtHuwJGm1v5Ojy0Z0LaSYhHaLJQB0P8Jw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-minify-selectors@5.2.1:
    resolution: {integrity: sha512-nPJu7OjZJTsVUmPdm2TcaiohIwxP+v8ha9NehQ2ye9szv4orirRU3SDdtUmKH+10nzn0bAyOXZ0UEr7OpvLehg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-modules-extract-imports@3.0.0:
    resolution: {integrity: sha512-bdHleFnP3kZ4NYDhuGlVK+CMrQ/pqUm8bx/oGL93K6gVwiclvX5x0n76fYMKuIGKzlABOy13zsvqjb0f92TEXw==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-local-by-default@4.0.0:
    resolution: {integrity: sha512-sT7ihtmGSF9yhm6ggikHdV0hlziDTX7oFoXtuVWeDd3hHObNkcHRo9V3yg7vCAY7cONyxJC/XXCmmiHHcvX7bQ==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-scope@3.0.0:
    resolution: {integrity: sha512-hncihwFA2yPath8oZ15PZqvWGkWf+XUfQgUGamS4LqoP1anQLOsOJw0vr7J7IwLpoY9fatA2qiGUGmuZL0Iqlg==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-values@4.0.0:
    resolution: {integrity: sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-nested@6.0.0:
    resolution: {integrity: sha512-0DkamqrPcmkBDsLn+vQDIrtkSbNkv5AD/M322ySo9kqFkCIYklym2xEmWkwo+Y3/qZo34tzEPNUw4y7yMCdv5w==}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.2.14

  postcss-nesting@10.2.0:
    resolution: {integrity: sha512-EwMkYchxiDiKUhlJGzWsD9b2zvq/r2SSubcRrgP+jujMXFzqvANLt16lJANC+5uZ6hjI7lpRmI6O8JIl+8l1KA==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-normalize-charset@5.1.0:
    resolution: {integrity: sha512-mSgUJ+pd/ldRGVx26p2wz9dNZ7ji6Pn8VWBajMXFf8jk7vUoSrZ2lt/wZR7DtlZYKesmZI680qjr2CeFF2fbUg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize-display-values@5.1.0:
    resolution: {integrity: sha512-WP4KIM4o2dazQXWmFaqMmcvsKmhdINFblgSeRgn8BJ6vxaMyaJkwAzpPpuvSIoG/rmX3M+IrRZEz2H0glrQNEA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize-positions@5.1.1:
    resolution: {integrity: sha512-6UpCb0G4eofTCQLFVuI3EVNZzBNPiIKcA1AKVka+31fTVySphr3VUgAIULBhxZkKgwLImhzMR2Bw1ORK+37INg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize-repeat-style@5.1.1:
    resolution: {integrity: sha512-mFpLspGWkQtBcWIRFLmewo8aC3ImN2i/J3v8YCFUwDnPu3Xz4rLohDO26lGjwNsQxB3YF0KKRwspGzE2JEuS0g==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize-string@5.1.0:
    resolution: {integrity: sha512-oYiIJOf4T9T1N4i+abeIc7Vgm/xPCGih4bZz5Nm0/ARVJ7K6xrDlLwvwqOydvyL3RHNf8qZk6vo3aatiw/go3w==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize-timing-functions@5.1.0:
    resolution: {integrity: sha512-DOEkzJ4SAXv5xkHl0Wa9cZLF3WCBhF3o1SKVxKQAa+0pYKlueTpCgvkFAHfk+Y64ezX9+nITGrDZeVGgITJXjg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize-unicode@5.1.1:
    resolution: {integrity: sha512-qnCL5jzkNUmKVhZoENp1mJiGNPcsJCs1aaRmURmeJGES23Z/ajaln+EPTD+rBeNkSryI+2WTdW+lwcVdOikrpA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize-url@5.1.0:
    resolution: {integrity: sha512-5upGeDO+PVthOxSmds43ZeMeZfKH+/DKgGRD7TElkkyS46JXAUhMzIKiCa7BabPeIy3AQcTkXwVVN7DbqsiCew==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-normalize-whitespace@5.1.1:
    resolution: {integrity: sha512-83ZJ4t3NUDETIHTa3uEg6asWjSBYL5EdkVB0sDncx9ERzOKBVJIUeDO9RyA9Zwtig8El1d79HBp0JEi8wvGQnA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-opacity-percentage@1.1.3:
    resolution: {integrity: sha512-An6Ba4pHBiDtyVpSLymUUERMo2cU7s+Obz6BTrS+gxkbnSBNKSuD0AVUc+CpBMrpVPKKfoVz0WQCX+Tnst0i4A==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-ordered-values@5.1.3:
    resolution: {integrity: sha512-9UO79VUhPwEkzbb3RNpqqghc6lcYej1aveQteWY+4POIwlqkYE21HKWaLDF6lWNuqCobEAyTovVhtI32Rbv2RQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-overflow-shorthand@3.0.4:
    resolution: {integrity: sha512-otYl/ylHK8Y9bcBnPLo3foYFLL6a6Ak+3EQBPOTR7luMYCOsiVTUk1iLvNf6tVPNGXcoL9Hoz37kpfriRIFb4A==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-page-break@3.0.4:
    resolution: {integrity: sha512-1JGu8oCjVXLa9q9rFTo4MbeeA5FMe00/9C7lN4va606Rdb+HkxXtXsmEDrIraQ11fGz/WvKWa8gMuCKkrXpTsQ==}
    peerDependencies:
      postcss: ^8

  postcss-place@7.0.5:
    resolution: {integrity: sha512-wR8igaZROA6Z4pv0d+bvVrvGY4GVHihBCBQieXFY3kuSuMyOmEnnfFzHl/tQuqHZkfkIVBEbDvYcFfHmpSet9g==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-preset-env@7.8.3:
    resolution: {integrity: sha512-T1LgRm5uEVFSEF83vHZJV2z19lHg4yJuZ6gXZZkqVsqv63nlr6zabMH3l4Pc01FQCyfWVrh2GaUeCVy9Po+Aag==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-pseudo-class-any-link@7.1.6:
    resolution: {integrity: sha512-9sCtZkO6f/5ML9WcTLcIyV1yz9D1rf0tWc+ulKcvV30s0iZKS/ONyETvoWsr6vnrmW+X+KmuK3gV/w5EWnT37w==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-reduce-initial@5.1.2:
    resolution: {integrity: sha512-dE/y2XRaqAi6OvjzD22pjTUQ8eOfc6m/natGHgKFBK9DxFmIm69YmaRVQrGgFlEfc1HePIurY0TmDeROK05rIg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-reduce-transforms@5.1.0:
    resolution: {integrity: sha512-2fbdbmgir5AvpW9RLtdONx1QoYG2/EtqpNQbFASDlixBbAYuTcJ0dECwlqNqH7VbaUnEnh8SrxOe2sRIn24XyQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-replace-overflow-wrap@4.0.0:
    resolution: {integrity: sha512-KmF7SBPphT4gPPcKZc7aDkweHiKEEO8cla/GjcBK+ckKxiZslIu3C4GCRW3DNfL0o7yW7kMQu9xlZ1kXRXLXtw==}
    peerDependencies:
      postcss: ^8.0.3

  postcss-selector-not@6.0.1:
    resolution: {integrity: sha512-1i9affjAe9xu/y9uqWH+tD4r6/hDaXJruk8xn2x1vzxC2U3J3LKO3zJW4CyxlNhA56pADJ/djpEwpH1RClI2rQ==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  postcss-selector-parser@6.0.11:
    resolution: {integrity: sha512-zbARubNdogI9j7WY4nQJBiNqQf3sLS3wCP4WfOidu+p28LofJqDH1tcXypGrcmMHhDk2t9wGhCsYe/+szLTy1g==}
    engines: {node: '>=4'}

  postcss-svgo@5.1.0:
    resolution: {integrity: sha512-D75KsH1zm5ZrHyxPakAxJWtkyXew5qwS70v56exwvw542d9CRtTo78K0WeFxZB4G7JXKKMbEZtZayTGdIky/eA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-unique-selectors@5.1.1:
    resolution: {integrity: sha512-5JiODlELrz8L2HwxfPnhOWZYWDxVHWL83ufOv84NrcgipI7TaeRsatAhK4Tr2/ZiYldpK/wBvw5BD3qfaK96GA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}

  postcss@8.4.21:
    resolution: {integrity: sha512-tP7u/Sn/dVxK2NnruI4H9BG+x+Wxz6oeZ1cJ8P6G/PZY0IKk4k/63TDsQf2kQq3+qoJeLm2kIBUNlZe3zgb4Zg==}
    engines: {node: ^10 || ^12 || >=14}

  postcss@8.4.38:
    resolution: {integrity: sha512-Wglpdk03BSfXkHoQa3b/oulrotAkwrlLDRSOb9D0bN86FdRyE9lppSp33aHNPgBa0JKCoB+drFLZkQoRRYae5A==}
    engines: {node: ^10 || ^12 || >=14}

  prelude-ls@1.1.2:
    resolution: {integrity: sha512-ESF23V4SKG6lVSGZgYNpbsiaAkdab6ZgOxe52p7+Kid3W3u3bxR4Vfd/o21dmN7jSt0IwgZ4v5MUd26FEtXE9w==}
    engines: {node: '>= 0.8.0'}

  prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}

  prettier-linter-helpers@1.0.0:
    resolution: {integrity: sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==}
    engines: {node: '>=6.0.0'}

  prettier@3.2.5:
    resolution: {integrity: sha512-3/GWa9aOC0YeD7LUfvOG2NiDyhOWRvt1k+rcKhOuYnMY24iiCphgneUfJDyFXd6rZCAnuLBv6UeAULtrhT/F4A==}
    engines: {node: '>=14'}
    hasBin: true

  pretty-error@4.0.0:
    resolution: {integrity: sha512-AoJ5YMAcXKYxKhuJGdcvse+Voc6v1RgnsR3nWcYU7q4t6z0Q6T86sv5Zq8VIRbOWWFpvdGE83LtdSMNd+6Y0xw==}

  pretty-time@1.1.0:
    resolution: {integrity: sha512-28iF6xPQrP8Oa6uxE6a1biz+lWeTOAPKggvjB8HAs6nVMKZwf5bG++632Dx614hIWgUPkgivRfG+a8uAXGTIbA==}
    engines: {node: '>=4'}

  process-nextick-args@2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==}

  process@0.11.10:
    resolution: {integrity: sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==}
    engines: {node: '>= 0.6.0'}

  progress@2.0.3:
    resolution: {integrity: sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==}
    engines: {node: '>=0.4.0'}

  promptly@2.2.0:
    resolution: {integrity: sha512-aC9j+BZsRSSzEsXBNBwDnAxujdx19HycZoKgRgzWnS8eOHg1asuf9heuLprfbe739zY3IdUQx+Egv6Jn135WHA==}

  prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}

  proxy-addr@2.0.7:
    resolution: {integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==}
    engines: {node: '>= 0.10'}

  proxy-agent@5.0.0:
    resolution: {integrity: sha512-gkH7BkvLVkSfX9Dk27W6TyNOWWZWRilRfk1XxGNWOYJ2TuedAv1yFpCaU9QSBmBe716XOTNpYNOzhysyw8xn7g==}
    engines: {node: '>= 8'}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  prr@1.0.1:
    resolution: {integrity: sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw==}

  pstree.remy@1.1.8:
    resolution: {integrity: sha512-77DZwxQmxKnu3aR542U+X8FypNzbfJ+C5XQDk3uWjWxn6151aIMGthWYRXTqT1E5oJvg+ljaa2OJi+VfvCOQ8w==}

  punycode@1.3.2:
    resolution: {integrity: sha512-RofWgt/7fL5wP1Y7fxE7/EmTLzQVnB0ycyibJ0OOHIlJqTNzglYFxVwETOcIoJqJmpDXJ9xImDv+Fq34F/d4Dw==}

  punycode@2.3.0:
    resolution: {integrity: sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA==}
    engines: {node: '>=6'}

  qs@6.11.0:
    resolution: {integrity: sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==}
    engines: {node: '>=0.6'}

  qs@6.11.1:
    resolution: {integrity: sha512-0wsrzgTz/kAVIeuxSjnpGC56rzYtr6JT/2BwEvMaPhFIoYa1aGO8LbzuU1R0uUYQkLpWBTOj0l/CLAJB64J6nQ==}
    engines: {node: '>=0.6'}

  querystring@0.2.0:
    resolution: {integrity: sha512-X/xY82scca2tau62i9mDyU9K+I+djTMUsvwf7xnUX5GLvVzgJybOJf4Y6o9Zx3oJK/LSXg5tTZBjwzqVPaPO2g==}
    engines: {node: '>=0.4.x'}
    deprecated: The querystring API is considered Legacy. new code should use the URLSearchParams API instead.

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  quick-lru@5.1.1:
    resolution: {integrity: sha512-WuyALRjWPDGtt/wzJiadO5AXY+8hZ80hVpe6MyivgraREW751X3SbhRvG3eLKOYN+8VEvqLcf3wdnt44Z4S4SA==}
    engines: {node: '>=10'}

  randombytes@2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}

  range-parser@1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}

  raw-body@2.5.1:
    resolution: {integrity: sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==}
    engines: {node: '>= 0.8'}

  rc-align@4.0.15:
    resolution: {integrity: sha512-wqJtVH60pka/nOX7/IspElA8gjPNQKIx/ZqJ6heATCkXpe1Zg4cPVrMD2vC96wjsFFL8WsmhPbx9tdMo1qqlIA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-dropdown@4.0.1:
    resolution: {integrity: sha512-OdpXuOcme1rm45cR0Jzgfl1otzmU4vuBVb+etXM8vcaULGokAKVpKlw8p6xzspG7jGd/XxShvq+N3VNEfk/l5g==}
    peerDependencies:
      react: '>=16.11.0'
      react-dom: '>=16.11.0'

  rc-motion@2.7.3:
    resolution: {integrity: sha512-2xUvo8yGHdOHeQbdI8BtBsCIrWKchEmFEIskf0nmHtJsou+meLd/JE+vnvSX2JxcBrJtXY2LuBpxAOxrbY/wMQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-trigger@5.3.4:
    resolution: {integrity: sha512-mQv+vas0TwKcjAO2izNPkqR4j86OemLRmvL2nOzdP9OWNWA1ivoTt5hzFqYNW9zACwmTezRiN8bttrC7cZzYSw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-util@5.30.0:
    resolution: {integrity: sha512-uaWpF/CZGyXuhQG71MWxkU+0bWkPEgqZUxEv251Cu7p3kpHDNm5+Ygu/U8ux0a/zbfGW8PsKcJL0XVBOMrlIZg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  react-dom@18.3.1:
    resolution: {integrity: sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==}
    peerDependencies:
      react: ^18.3.1

  react-fast-compare@3.2.1:
    resolution: {integrity: sha512-xTYf9zFim2pEif/Fw16dBiXpe0hoy5PxcD8+OwBnTtNLfIm3g6WxhKNurY+6OmdH1u6Ta/W/Vl6vjbYP1MFnDg==}

  react-helmet-async@1.3.0:
    resolution: {integrity: sha512-9jZ57/dAn9t3q6hneQS0wukqC2ENOBgMNVEhb/ZG9ZSxUetzVIw4iAmEU38IaVg3QGYauQPhSeUTuIUtFglWpg==}
    peerDependencies:
      react: ^16.6.0 || ^17.0.0 || ^18.0.0
      react-dom: ^16.6.0 || ^17.0.0 || ^18.0.0

  react-intl@6.4.0:
    resolution: {integrity: sha512-SSEPVC9B2T0WV21SG5R1kJ/3uxQyu7RB8AchNyuMvBq9588ilKgNLy4+oGKoK0dTvLeZWYzhlc7z9VoLFifjLQ==}
    peerDependencies:
      react: ^16.6.0 || 17 || 18
      typescript: ^4.7 || 5
    peerDependenciesMeta:
      typescript:
        optional: true

  react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  react-is@18.2.0:
    resolution: {integrity: sha512-xWGDIW6x921xtzPkhiULtthJHoJvBbF3q26fzloPCK0hsvxtPVelvftw3zjbHWSkR2km9Z+4uxbDDK/6Zw9B8w==}

  react-refresh@0.14.0:
    resolution: {integrity: sha512-wViHqhAd8OHeLS/IRMJjTSDHF3U9eWi62F/MledQGPdJGDhodXJ9PBLNGr6WWL7qlH12Mt3TyTpbS+hGXMjCzQ==}
    engines: {node: '>=0.10.0'}

  react-router-dom@6.10.0:
    resolution: {integrity: sha512-E5dfxRPuXKJqzwSe/qGcqdwa18QiWC6f3H3cWXM24qj4N0/beCIf/CWTipop2xm7mR0RCS99NnaqPNjHtrAzCg==}
    engines: {node: '>=14'}
    peerDependencies:
      react: '>=16.8'
      react-dom: '>=16.8'

  react-router@6.10.0:
    resolution: {integrity: sha512-Nrg0BWpQqrC3ZFFkyewrflCud9dio9ME3ojHCF/WLsprJVzkq3q3UeEhMCAW1dobjeGbWgjNn/PVF6m46ANxXQ==}
    engines: {node: '>=14'}
    peerDependencies:
      react: '>=16.8'

  react@18.3.1:
    resolution: {integrity: sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==}
    engines: {node: '>=0.10.0'}

  read-cache@1.0.0:
    resolution: {integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==}

  read@1.0.7:
    resolution: {integrity: sha512-rSOKNYUmaxy0om1BNjMN4ezNT6VKK+2xF4GBhc81mkH7L60i6dp8qPYrkndNLT3QPphoII3maL9PVC9XmhHwVQ==}
    engines: {node: '>=0.8'}

  readable-stream@1.1.14:
    resolution: {integrity: sha512-+MeVjFf4L44XUkhM1eYbD8fyEsxcV81pqMSR5gblfcLCHfZvbrqy4/qYHE+/R5HoBUT11WV5O08Cr1n3YXkWVQ==}

  readable-stream@2.3.8:
    resolution: {integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==}

  readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}

  readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}

  rechoir@0.8.0:
    resolution: {integrity: sha512-/vxpCXddiX8NGfGO/mTafwjq4aFa/71pvamip0++IQk3zG8cbCj0fifNPrjjF1XMXUne91jL9OoxmdykoEtifQ==}
    engines: {node: '>= 10.13.0'}

  regenerate-unicode-properties@10.1.0:
    resolution: {integrity: sha512-d1VudCLoIGitcU/hEg2QqvyGZQmdC0Lf8BqdOMXGFSvJP4bNV1+XqbPQeHHLD51Jh4QJJ225dlIFvY4Ly6MXmQ==}
    engines: {node: '>=4'}

  regenerate@1.4.2:
    resolution: {integrity: sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==}

  regenerator-runtime@0.13.11:
    resolution: {integrity: sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==}

  regenerator-transform@0.15.1:
    resolution: {integrity: sha512-knzmNAcuyxV+gQCufkYcvOqX/qIIfHLv0u5x79kRxuGojfYVky1f15TzZEu2Avte8QGepvUNTnLskf8E6X6Vyg==}

  regexp.prototype.flags@1.4.3:
    resolution: {integrity: sha512-fjggEOO3slI6Wvgjwflkc4NFRCTZAu5CnNfBd5qOMYhWdn67nJBBu34/TkD++eeFmd8C9r9jfXJ27+nSiRkSUA==}
    engines: {node: '>= 0.4'}

  regexpp@3.2.0:
    resolution: {integrity: sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg==}
    engines: {node: '>=8'}

  regexpu-core@5.3.2:
    resolution: {integrity: sha512-RAM5FlZz+Lhmo7db9L298p2vHP5ZywrVXmVXpmAD9GuL5MPH6t9ROw1iA/wfHkQ76Qe7AaPF0nGuim96/IrQMQ==}
    engines: {node: '>=4'}

  regjsparser@0.9.1:
    resolution: {integrity: sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ==}
    hasBin: true

  relateurl@0.2.7:
    resolution: {integrity: sha512-G08Dxvm4iDN3MLM0EsP62EDV9IuhXPR6blNz6Utcp7zyV3tr4HVNINt6MpaRWbxoOHT3Q7YN2P+jaHX8vUbgog==}
    engines: {node: '>= 0.10'}

  renderkid@3.0.0:
    resolution: {integrity: sha512-q/7VIQA8lmM1hF+jn+sFSPWGlMkSAeNYcPLmDQx2zzuiDfaLrOmumR8iaUKlenFgh0XRPIUeSPlH3A+AW3Z5pg==}

  require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}

  require-in-the-middle@5.2.0:
    resolution: {integrity: sha512-efCx3b+0Z69/LGJmm9Yvi4cqEdxnoGnxYxGxBghkkTTFeXRtTCmmhO0AnAfHz59k957uTSuy8WaHqOs8wbYUWg==}
    engines: {node: '>=6'}

  requires-port@1.0.0:
    resolution: {integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==}

  resize-observer-polyfill@1.5.1:
    resolution: {integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==}

  resolve-cwd@3.0.0:
    resolution: {integrity: sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==}
    engines: {node: '>=8'}

  resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  resolve-from@5.0.0:
    resolution: {integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==}
    engines: {node: '>=8'}

  resolve-path@1.4.0:
    resolution: {integrity: sha512-i1xevIst/Qa+nA9olDxLWnLk8YZbi8R/7JPbCMcgyWaFR6bKWaexgJgEB5oc2PKMjYdrHynyz0NY+if+H98t1w==}
    engines: {node: '>= 0.8'}

  resolve@1.22.2:
    resolution: {integrity: sha512-Sb+mjNHOULsBv818T40qSPeRiuWLyaGMa5ewydRLFimneixmVy2zdivRl+AF6jaYPC8ERxGDmFSiqui6SfPd+g==}
    hasBin: true

  resolve@2.0.0-next.4:
    resolution: {integrity: sha512-iMDbmAWtfU+MHpxt/I5iWI7cY6YVEZUQ3MBgPQ++XD1PELuJHIl82xBmObyP2KyQmkNB2dsqF7seoQQiAn5yDQ==}
    hasBin: true

  restore-cursor@2.0.0:
    resolution: {integrity: sha512-6IzJLuGi4+R14vwagDHX+JrXmPVtPpn4mffDJ1UdR7/Edm87fl6yi8mMBIVvFtJaNTUvjughmW4hwLhRG7gC1Q==}
    engines: {node: '>=4'}

  retry@0.13.1:
    resolution: {integrity: sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==}
    engines: {node: '>= 4'}

  reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    hasBin: true

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  run-series@1.1.9:
    resolution: {integrity: sha512-Arc4hUN896vjkqCYrUXquBFtRZdv1PfLbTYP71efP6butxyQ0kWpiNJyAgsxscmQg1cqvHY32/UCBzXedTpU2g==}

  safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  safe-regex-test@1.0.0:
    resolution: {integrity: sha512-JBUUzyOgEwXQY1NuPtvcj/qcBDbDmEvWufhlnXZIm75DEHp+afM1r1ujJpJsV/gSM4t59tpDyPi1sd6ZaPFfsA==}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  sax@1.2.1:
    resolution: {integrity: sha512-8I2a3LovHTOpm7NV5yOyO8IHqgVsfK4+UuySrXU8YXkSRX7k6hCV9b3HrkKCr3nMpgj+0bmocaJJWpvp1oc7ZA==}

  sax@1.2.4:
    resolution: {integrity: sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw==}

  scheduler@0.23.2:
    resolution: {integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==}

  schema-utils@2.7.1:
    resolution: {integrity: sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg==}
    engines: {node: '>= 8.9.0'}

  schema-utils@3.1.1:
    resolution: {integrity: sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw==}
    engines: {node: '>= 10.13.0'}

  schema-utils@4.0.0:
    resolution: {integrity: sha512-1edyXKgh6XnJsJSQ8mKWXnN/BVaIbFMLpouRUrXgVq7WYne5kw3MW7UPhO44uRXQSIpTSXoJbmrR2X0w9kUTyg==}
    engines: {node: '>= 12.13.0'}

  select-hose@2.0.0:
    resolution: {integrity: sha512-mEugaLK+YfkijB4fx0e6kImuJdCIt2LxCRcbEYPqRGCs4F2ogyfZU5IAZRdjCP8JPq2AtdNoC/Dux63d9Kiryg==}

  selfsigned@2.1.1:
    resolution: {integrity: sha512-GSL3aowiF7wa/WtSFwnUrludWFoNhftq8bUkH9pkzjpN2XSPOAYEgg6e0sS9s0rZwgJzJiQRPU18A6clnoW5wQ==}
    engines: {node: '>=10'}

  semver-compare@1.0.0:
    resolution: {integrity: sha512-YM3/ITh2MJ5MtzaM429anh+x2jiLVjqILF4m4oyQB18W7Ggea7BfqdH/wGMK7dDiMghv/6WG7znWMwUDzJiXow==}

  semver@5.7.1:
    resolution: {integrity: sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==}
    hasBin: true

  semver@6.3.0:
    resolution: {integrity: sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==}
    hasBin: true

  semver@7.2.3:
    resolution: {integrity: sha512-utbW9Z7ZxVvwiIWkdOMLOR9G/NFXh2aRucghkVrEMJWuC++r3lCkBC3LwqBinyHzGMAJxY5tn6VakZGHObq5ig==}
    engines: {node: '>=10'}
    hasBin: true

  semver@7.3.8:
    resolution: {integrity: sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A==}
    engines: {node: '>=10'}
    hasBin: true

  semver@7.5.3:
    resolution: {integrity: sha512-QBlUtyVk/5EeHbi7X0fw6liDZc7BBmEaSYn01fMU1OUYbf6GPsbTtd8WmnqbI20SeycoHSeiybkE/q1Q+qlThQ==}
    engines: {node: '>=10'}
    hasBin: true

  send@0.18.0:
    resolution: {integrity: sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==}
    engines: {node: '>= 0.8.0'}

  serialize-javascript@6.0.1:
    resolution: {integrity: sha512-owoXEFjWRllis8/M1Q+Cw5k8ZH40e3zhp/ovX+Xr/vi1qj6QesbyXXViFbpNvWvPNAD62SutwEXavefrLJWj7w==}

  serve-index@1.9.1:
    resolution: {integrity: sha512-pXHfKNP4qujrtteMrSBb0rc8HJ9Ms/GrXwcUtUtD5s4ewDJI8bT3Cz2zTVRMKtri49pLx2e0Ya8ziP5Ya2pZZw==}
    engines: {node: '>= 0.8.0'}

  serve-static@1.15.0:
    resolution: {integrity: sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==}
    engines: {node: '>= 0.8.0'}

  server-destroy@1.0.1:
    resolution: {integrity: sha512-rb+9B5YBIEzYcD6x2VKidaa+cqYBJQKnU4oe4E3ANwRRN56yk/ua1YCJT1n21NTS8w6CcOclAKNP3PhdCXKYtQ==}

  serverless-http@3.2.0:
    resolution: {integrity: sha512-QvSyZXljRLIGqwcJ4xsKJXwkZnAVkse1OajepxfjkBXV0BMvRS5R546Z4kCBI8IygDzkQY0foNPC/rnipaE9pQ==}
    engines: {node: '>=12.0'}

  setprototypeof@1.1.0:
    resolution: {integrity: sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==}

  setprototypeof@1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}

  shallow-clone@3.0.1:
    resolution: {integrity: sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==}
    engines: {node: '>=8'}

  shallowequal@1.1.0:
    resolution: {integrity: sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ==}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  shell-quote@1.8.0:
    resolution: {integrity: sha512-QHsz8GgQIGKlRi24yFc6a6lN69Idnx634w49ay6+jA5yFh7a1UY+4Rp6HPx/L/1zcEDPEij8cIsiqR6bQsE5VQ==}

  shellwords@0.1.1:
    resolution: {integrity: sha512-vFwSUfQvqybiICwZY5+DAWIPLKsWO31Q91JSKl3UYv+K5c2QRPzn0qzec6QPu1Qc9eHYItiP3NdJqNVqetYAww==}

  shimmer@1.2.1:
    resolution: {integrity: sha512-sQTKC1Re/rM6XyFM6fIAGHRPVGvyXfgzIDvzoq608vM+jeyVD0Tu1E6Np0Kc2zAIFWIj963V2800iF/9LPieQw==}

  side-channel@1.0.4:
    resolution: {integrity: sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==}

  signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}

  simple-progress-webpack-plugin@1.1.2:
    resolution: {integrity: sha512-bNQfb3qSqbtsfxg6d0dGechUUJH2lZqKG5+bj2aoJmEA0rSzcm+2JVfC2YgkDABfuGItZ/O5ttt6BssWZW4SNg==}
    peerDependencies:
      webpack: '>=2.0.0'

  simple-update-notifier@2.0.0:
    resolution: {integrity: sha512-a2B9Y0KlNXl9u/vsW6sTIu9vGEpfKu2wRV6l1H3XEas/0gUIzGzBoP/IouTcUQbm9JWZLH3COxyn03TYlFax6w==}
    engines: {node: '>=10'}

  sirv@1.0.19:
    resolution: {integrity: sha512-JuLThK3TnZG1TAKDwNIqNq6QA2afLOCcm+iE8D1Kj3GA40pSPsxQjjJl0J8X3tsR7T+CP1GavpzLwYkgVLWrZQ==}
    engines: {node: '>= 10'}

  slash@4.0.0:
    resolution: {integrity: sha512-3dOsAHXXUkQTpOYcoAxLIorMTp4gIQr5IW3iVb7A7lFIp0VHhnynm9izx6TssdrIcVIESAlVjtnO2K8bg+Coew==}
    engines: {node: '>=12'}

  slice-ansi@4.0.0:
    resolution: {integrity: sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==}
    engines: {node: '>=10'}

  smart-buffer@4.2.0:
    resolution: {integrity: sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==}
    engines: {node: '>= 6.0.0', npm: '>= 3.0.0'}

  sockjs@0.3.24:
    resolution: {integrity: sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ==}

  socks-proxy-agent@5.0.1:
    resolution: {integrity: sha512-vZdmnjb9a2Tz6WEQVIurybSwElwPxMZaIc7PzqbJTrezcKNznv6giT7J7tZDZ1BojVaa1jvO/UiUdhDVB0ACoQ==}
    engines: {node: '>= 6'}

  socks@2.7.1:
    resolution: {integrity: sha512-7maUZy1N7uo6+WVEX6psASxtNlKaNVMlGQKkG/63nEDdLOWNbiUMoLK7X4uYoLhQstau72mLgfEWcXcwsaHbYQ==}
    engines: {node: '>= 10.13.0', npm: '>= 3.0.0'}

  source-list-map@2.0.1:
    resolution: {integrity: sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw==}

  source-map-js@1.0.2:
    resolution: {integrity: sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==}
    engines: {node: '>=0.10.0'}

  source-map-js@1.2.0:
    resolution: {integrity: sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg==}
    engines: {node: '>=0.10.0'}

  source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  source-map@0.7.4:
    resolution: {integrity: sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==}
    engines: {node: '>= 8'}

  spdy-transport@3.0.0:
    resolution: {integrity: sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw==}

  spdy@4.0.2:
    resolution: {integrity: sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA==}
    engines: {node: '>=6.0.0'}

  sprintf-js@1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==}

  sprintf-js@1.1.2:
    resolution: {integrity: sha512-VE0SOVEHCk7Qc8ulkWw3ntAzXuqf7S2lvwQaDLRnUeIEaKNQJzV6BwmLKhOqT61aGhfUMrXeaBk+oDGCzvhcug==}

  stable@0.1.8:
    resolution: {integrity: sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==}
    deprecated: 'Modern JS already guarantees Array#sort() is a stable sort, so this library is deprecated. See the compatibility table on MDN: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#browser_compatibility'

  stackframe@1.3.4:
    resolution: {integrity: sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==}

  standard-engine@15.0.0:
    resolution: {integrity: sha512-4xwUhJNo1g/L2cleysUqUv7/btn7GEbYJvmgKrQ2vd/8pkTmN8cpqAZg+BT8Z1hNeEH787iWUdOpL8fmApLtxA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  standard@17.0.0:
    resolution: {integrity: sha512-GlCM9nzbLUkr+TYR5I2WQoIah4wHA2lMauqbyPLV/oI5gJxqhHzhjl9EG2N0lr/nRqI3KCbCvm/W3smxvLaChA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    hasBin: true

  statuses@1.5.0:
    resolution: {integrity: sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==}
    engines: {node: '>= 0.6'}

  statuses@2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}

  std-env@3.3.2:
    resolution: {integrity: sha512-uUZI65yrV2Qva5gqE0+A7uVAvO40iPo6jGhs7s8keRfHCmtg+uB2X6EiLGCI9IgL1J17xGhvoOqSz79lzICPTA==}

  steno@0.4.4:
    resolution: {integrity: sha512-EEHMVYHNXFHfGtgjNITnka0aHhiAlo93F7z2/Pwd+g0teG9CnM3JIINM7hVVB5/rhw9voufD7Wukwgtw2uqh6w==}

  string-width@2.1.1:
    resolution: {integrity: sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==}
    engines: {node: '>=4'}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string.prototype.matchall@4.0.8:
    resolution: {integrity: sha512-6zOCOcJ+RJAQshcTvXPHoxoQGONa3e/Lqx90wUA+wEzX78sg5Bo+1tQo4N0pohS0erG9qtCqJDjNCQBjeWVxyg==}

  string.prototype.trim@1.2.7:
    resolution: {integrity: sha512-p6TmeT1T3411M8Cgg9wBTMRtY2q9+PNy9EV1i2lIXUN/btt763oIfxwN3RR8VU6wHX8j/1CFy0L+YuThm6bgOg==}
    engines: {node: '>= 0.4'}

  string.prototype.trimend@1.0.6:
    resolution: {integrity: sha512-JySq+4mrPf9EsDBEDYMOb/lM7XQLulwg5R/m1r0PXEFqrV0qHvl58sdTilSXtKOflCsK2E8jxf+GKC0T07RWwQ==}

  string.prototype.trimstart@1.0.6:
    resolution: {integrity: sha512-omqjMDaY92pbn5HOX7f9IccLA+U1tA9GvtU4JrodiXFfYB7jPzzHpRzpglLAjtUV6bB557zwClJezTqnAiYnQA==}

  string_decoder@0.10.31:
    resolution: {integrity: sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ==}

  string_decoder@1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==}

  string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}

  strip-ansi@3.0.1:
    resolution: {integrity: sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==}
    engines: {node: '>=0.10.0'}

  strip-ansi@4.0.0:
    resolution: {integrity: sha512-4XaJ2zQdCzROZDivEVIDPkcQn8LMFSa8kj8Gxb/Lnwzv9A8VctNZ+lfivC/sV3ivW8ElJTERXZoPBRrZKkNKow==}
    engines: {node: '>=4'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-bom@3.0.0:
    resolution: {integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==}
    engines: {node: '>=4'}

  strip-final-newline@2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}

  style-resources-loader@1.5.0:
    resolution: {integrity: sha512-fIfyvQ+uvXaCBGGAgfh+9v46ARQB1AWdaop2RpQw0PBVuROsTBqGvx8dj0kxwjGOAyq3vepe4AOK3M6+Q/q2jw==}
    engines: {node: '>=8.9'}
    peerDependencies:
      webpack: ^3.0.0 || ^4.0.0 || ^5.0.0

  styled-components@5.3.9:
    resolution: {integrity: sha512-Aj3kb13B75DQBo2oRwRa/APdB5rSmwUfN5exyarpX+x/tlM/rwZA2vVk2vQgVSP6WKaZJHWwiFrzgHt+CLtB4A==}
    engines: {node: '>=10'}
    peerDependencies:
      react: '>= 16.8.0'
      react-dom: '>= 16.8.0'
      react-is: '>= 16.8.0'

  stylehacks@5.1.1:
    resolution: {integrity: sha512-sBpcd5Hx7G6seo7b1LkpttvTz7ikD0LlH5RmdcBNb6fFR0Fl7LQwHDFr300q4cwUqi+IYrFGmsIHieMBfnN/Bw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15

  sucrase@3.32.0:
    resolution: {integrity: sha512-ydQOU34rpSyj2TGyz4D2p8rbktIOZ8QY9s+DGLvFU1i5pWJE8vkpruCjGCMHsdXwnD7JDcS+noSwM/a7zyNFDQ==}
    engines: {node: '>=8'}
    hasBin: true

  supports-color@2.0.0:
    resolution: {integrity: sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g==}
    engines: {node: '>=0.8.0'}

  supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  supports-color@8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  svg-parser@2.0.4:
    resolution: {integrity: sha512-e4hG1hRwoOdRb37cIMSgzNsxyzKfayW6VOflrwvR+/bzrkyxY/31WkbgnQpgtrNp1SdpJvpUAGTa/ZoiPNDuRQ==}

  svgo@2.8.0:
    resolution: {integrity: sha512-+N/Q9kV1+F+UeWYoSiULYo4xYSDQlTgb+ayMobAXPwMnLvop7oxKMo9OzIrX5x3eS4L4f2UHhc9axXwY8DpChg==}
    engines: {node: '>=10.13.0'}
    hasBin: true

  systeminformation@5.18.6:
    resolution: {integrity: sha512-pLXv6kjJZ1xUcVs9SrCqbQ9y0x1rgRWxBUc8/KxpOp9IRxFGFfzVK5efsxBn/KdYog4C9rPcKk+kHNIL2SB/8Q==}
    engines: {node: '>=8.0.0'}
    os: [darwin, linux, win32, freebsd, openbsd, netbsd, sunos, android]
    hasBin: true

  table@6.8.1:
    resolution: {integrity: sha512-Y4X9zqrCftUhMeH2EptSSERdVKt/nEdijTOacGD/97EKjhQ/Qs8RTlEGABSJNNN8lac9kheH+af7yAkEWlgneA==}
    engines: {node: '>=10.0.0'}

  tailwindcss@3.3.1:
    resolution: {integrity: sha512-Vkiouc41d4CEq0ujXl6oiGFQ7bA3WEhUZdTgXAhtKxSy49OmKs8rEfQmupsfF0IGW8fv2iQkp1EVUuapCFrZ9g==}
    engines: {node: '>=12.13.0'}
    hasBin: true
    peerDependencies:
      postcss: ^8.0.9

  tapable@2.2.1:
    resolution: {integrity: sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==}
    engines: {node: '>=6'}

  terser-webpack-plugin@5.3.7:
    resolution: {integrity: sha512-AfKwIktyP7Cu50xNjXF/6Qb5lBNzYaWpU6YfoX3uZicTx0zTy0stDDCsvjDapKsSDvOeWo5MEq4TmdBy2cNoHw==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true

  terser@5.16.8:
    resolution: {integrity: sha512-QI5g1E/ef7d+PsDifb+a6nnVgC4F22Bg6T0xrBrz6iloVB4PUkkunp6V8nzoOOZJIzjWVdAGqCdlKlhLq/TbIA==}
    engines: {node: '>=10'}
    hasBin: true

  text-table@0.2.0:
    resolution: {integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==}

  thenify-all@1.6.0:
    resolution: {integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==}
    engines: {node: '>=0.8'}

  thenify@3.3.1:
    resolution: {integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==}

  thread-loader@3.0.4:
    resolution: {integrity: sha512-ByaL2TPb+m6yArpqQUZvP+5S1mZtXsEP7nWKKlAUTm7fCml8kB5s1uI3+eHRP2bk5mVYfRSBI7FFf+tWEyLZwA==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.27.0 || ^5.0.0

  thunky@1.1.0:
    resolution: {integrity: sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA==}

  to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  toidentifier@1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==}
    engines: {node: '>=0.6'}

  totalist@1.1.0:
    resolution: {integrity: sha512-gduQwd1rOdDMGxFG1gEvhV88Oirdo2p+KjoYFU7k2g+i7n6AFFbDQ5kMPUsW0pNbfQsB/cwXvT1i4Bue0s9g5g==}
    engines: {node: '>=6'}

  touch@3.1.0:
    resolution: {integrity: sha512-WBx8Uy5TLtOSRtIq+M03/sKDrXCLHxwDcquSP2c43Le03/9serjQBIztjRz6FkJez9D/hleyAXTBGLwwZUw9lA==}
    hasBin: true

  ts-interface-checker@0.1.13:
    resolution: {integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==}

  ts-node@10.9.2:
    resolution: {integrity: sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==}
    hasBin: true
    peerDependencies:
      '@swc/core': '>=1.2.50'
      '@swc/wasm': '>=1.2.50'
      '@types/node': '*'
      typescript: '>=2.7'
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      '@swc/wasm':
        optional: true

  tsconfig-paths-webpack-plugin@4.0.1:
    resolution: {integrity: sha512-m5//KzLoKmqu2MVix+dgLKq70MnFi8YL8sdzQZ6DblmCdfuq/y3OqvJd5vMndg2KEVCOeNz8Es4WVZhYInteLw==}
    engines: {node: '>=10.13.0'}

  tsconfig-paths@3.14.2:
    resolution: {integrity: sha512-o/9iXgCYc5L/JxCHPe3Hvh8Q/2xm5Z+p18PESBU6Ff33695QnCHBEjcytY2q19ua7Mbl/DavtBOLq+oG0RCL+g==}

  tsconfig-paths@4.2.0:
    resolution: {integrity: sha512-NoZ4roiN7LnbKn9QqE1amc9DJfzvZXxF4xDavcOWt1BPkdx+m+0gJuPM+S0vCe7zTJMYUP0R8pO2XMr+Y8oLIg==}
    engines: {node: '>=6'}

  tslib@1.9.3:
    resolution: {integrity: sha512-4krF8scpejhaOgqzBEcGM7yDIEfi0/8+8zDRZhNZZ2kjmHJ4hv3zCbQWxoJGz1iw5U0Jl0nma13xzHXcncMavQ==}

  tslib@2.5.0:
    resolution: {integrity: sha512-336iVw3rtn2BUK7ORdIAHTyxHGRIHVReokCR3XjbckJMK7ms8FysBfhLR8IXnAgy7T0PTPNBWKiH514FOW/WSg==}

  tsscmp@1.0.6:
    resolution: {integrity: sha512-LxhtAkPDTkVCMQjt2h6eBVY28KCjikZqZfMcC15YBeNjkgUpdCfBu5HoiOTDu86v6smE8yOjyEktJ8hlbANHQA==}
    engines: {node: '>=0.6.x'}

  tv4@1.3.0:
    resolution: {integrity: sha512-afizzfpJgvPr+eDkREK4MxJ/+r8nEEHcmitwgnPUqpaP+FpwQyadnxNoSACbgc/b1LsZYtODGoPiFxQrgJgjvw==}
    engines: {node: '>= 0.8.0'}

  tx2@1.0.5:
    resolution: {integrity: sha512-sJ24w0y03Md/bxzK4FU8J8JveYYUbSs2FViLJ2D/8bytSiyPRbuE3DyL/9UKYXTZlV3yXq0L8GLlhobTnekCVg==}

  type-check@0.3.2:
    resolution: {integrity: sha512-ZCmOJdvOWDBYJlzAoFkC+Q0+bUyEOS1ltgp1MGU03fqHG+dbi9tBFU2Rd9QKiDZFAYrhPh2JUf7rZRIuHRKtOg==}
    engines: {node: '>= 0.8.0'}

  type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}

  type-fest@0.20.2:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==}
    engines: {node: '>=10'}

  type-fest@0.3.1:
    resolution: {integrity: sha512-cUGJnCdr4STbePCgqNFbpVNCepa+kAVohJs1sLhxzdH+gnEoOd8VhbYa7pD3zZYGiURWM2xzEII3fQcRizDkYQ==}
    engines: {node: '>=6'}

  type-is@1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==}
    engines: {node: '>= 0.6'}

  typed-array-length@1.0.4:
    resolution: {integrity: sha512-KjZypGq+I/H7HI5HlOoGHkWUUGq+Q0TPhQurLbyrVrvnKTBgzLhIJ7j6J/XTQOi0d1RjyZ0wdas8bKs2p0x3Ng==}

  typescript@5.0.3:
    resolution: {integrity: sha512-xv8mOEDnigb/tN9PSMTwSEqAnUvkoXMQlicOb0IUVDBSQCgBSaAAROUZYy2IcUy5qU6XajK5jjjO7TMWqBTKZA==}
    engines: {node: '>=12.20'}
    hasBin: true

  unbox-primitive@1.0.2:
    resolution: {integrity: sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==}

  undefsafe@2.0.5:
    resolution: {integrity: sha512-WxONCrssBM8TSPRqN5EmsjVrsv4A8X12J4ArBiiayv3DyyG3ZlIg6yysuuSYdZsVz3TKcTg2fd//Ujd4CHV1iA==}

  unicode-canonical-property-names-ecmascript@2.0.0:
    resolution: {integrity: sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ==}
    engines: {node: '>=4'}

  unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==}
    engines: {node: '>=4'}

  unicode-match-property-value-ecmascript@2.1.0:
    resolution: {integrity: sha512-qxkjQt6qjg/mYscYMC0XKRn3Rh0wFPlfxB0xkt9CfyTvpX1Ra0+rAmdX2QyAobptSEvuy4RtpPRui6XkV+8wjA==}
    engines: {node: '>=4'}

  unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==}
    engines: {node: '>=4'}

  universalify@0.1.2:
    resolution: {integrity: sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==}
    engines: {node: '>= 4.0.0'}

  universalify@2.0.0:
    resolution: {integrity: sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ==}
    engines: {node: '>= 10.0.0'}

  unpipe@1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==}
    engines: {node: '>= 0.8'}

  update-browserslist-db@1.0.10:
    resolution: {integrity: sha512-OztqDenkfFkbSG+tRxBeAnCVPckDBcvibKd35yDONx6OU8N7sqgwc7rCbkJ/WcYtVRZ4ba68d6byhC21GFh7sQ==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  url@0.10.3:
    resolution: {integrity: sha512-hzSUW2q06EqL1gKM/a+obYHLIO6ct2hwPuviqTTOcfFVc61UbfJ2Q32+uGL/HCPxKqrdGB5QUwIe7UqlDgwsOQ==}

  use-sync-external-store@1.2.0:
    resolution: {integrity: sha512-eEgnFxGQ1Ife9bzYs6VLi8/4X6CObHMw9Qr9tPY43iKwsPw8xE8+EFsf/2cFZ5S3esXgpWgtSCtLNS41F+sKPA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  util@0.12.5:
    resolution: {integrity: sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==}

  utila@0.4.0:
    resolution: {integrity: sha512-Z0DbgELS9/L/75wZbro8xAnT50pBVFQZ+hUEueGDU5FN51YSCYM+jdxsfCiHjwNP/4LCDD0i/graKpeBnOXKRA==}

  utils-merge@1.0.1:
    resolution: {integrity: sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==}
    engines: {node: '>= 0.4.0'}

  uuid@3.4.0:
    resolution: {integrity: sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==}
    deprecated: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
    hasBin: true

  uuid@8.0.0:
    resolution: {integrity: sha512-jOXGuXZAWdsTH7eZLtyXMqUb9EcWMGZNbL9YcGBJl4MH4nrxHmZJhEHvyLFrkxo+28uLb/NYRcStH48fnD0Vzw==}
    hasBin: true

  uuid@8.3.2:
    resolution: {integrity: sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==}
    hasBin: true

  v8-compile-cache-lib@3.0.1:
    resolution: {integrity: sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==}

  v8-compile-cache@2.3.0:
    resolution: {integrity: sha512-l8lCEmLcLYZh4nbunNZvQCJc5pv7+RCwa8q/LdUx8u7lsWvPDKmpodJAJNwkAhJC//dFY48KuIEmjtd4RViDrA==}

  vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}

  vizion@2.2.1:
    resolution: {integrity: sha512-sfAcO2yeSU0CSPFI/DmZp3FsFE9T+8913nv1xWBOyzODv13fwkn6Vl7HqxGpkr9F608M+8SuFId3s+BlZqfXww==}
    engines: {node: '>=4.0'}

  vm2@3.9.19:
    resolution: {integrity: sha512-J637XF0DHDMV57R6JyVsTak7nIL8gy5KH4r1HiwWLf/4GBbb5MKL5y7LpmF4A8E2nR6XmzpmMFQ7V7ppPTmUQg==}
    engines: {node: '>=6.0'}
    hasBin: true

  watchpack@2.4.0:
    resolution: {integrity: sha512-Lcvm7MGST/4fup+ifyKi2hjyIAwcdI4HRgtvTpIUxBRhB+RFtUh8XtDOxUfctVCnhVi+QQj49i91OyvzkJl6cg==}
    engines: {node: '>=10.13.0'}

  wbuf@1.7.3:
    resolution: {integrity: sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA==}

  webpack-build-notifier@2.3.0:
    resolution: {integrity: sha512-+/s6GrvSRwP0CHqHOqZcta7EcqjL1MUR9KmBSlkJj6xJVZtFB/z68CTeLPrHnMED/umlXN9hcFV8wmGjeI/K2A==}

  webpack-bundle-analyzer@4.8.0:
    resolution: {integrity: sha512-ZzoSBePshOKhr+hd8u6oCkZVwpVaXgpw23ScGLFpR6SjYI7+7iIWYarjN6OEYOfRt8o7ZyZZQk0DuMizJ+LEIg==}
    engines: {node: '>= 10.13.0'}
    hasBin: true

  webpack-cli@5.0.1:
    resolution: {integrity: sha512-S3KVAyfwUqr0Mo/ur3NzIp6jnerNpo7GUO6so51mxLi1spqsA17YcMXy0WOIJtBSnj748lthxC6XLbNKh/ZC+A==}
    engines: {node: '>=14.15.0'}
    hasBin: true
    peerDependencies:
      '@webpack-cli/generators': '*'
      webpack: 5.x.x
      webpack-bundle-analyzer: '*'
      webpack-dev-server: '*'
    peerDependenciesMeta:
      '@webpack-cli/generators':
        optional: true
      webpack-bundle-analyzer:
        optional: true
      webpack-dev-server:
        optional: true

  webpack-dev-middleware@5.3.3:
    resolution: {integrity: sha512-hj5CYrY0bZLB+eTO+x/j67Pkrquiy7kWepMHmUMoPsmcUaeEnQJqFzHJOyxgWlq746/wUuA64p9ta34Kyb01pA==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0

  webpack-dev-middleware@6.0.2:
    resolution: {integrity: sha512-iOddiJzPcQC6lwOIu60vscbGWth8PCRcWRCwoQcTQf9RMoOWBHg5EyzpGdtSmGMrSPd5vHEfFXmVErQEmkRngQ==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      webpack: ^5.0.0
    peerDependenciesMeta:
      webpack:
        optional: true

  webpack-dev-server@4.13.2:
    resolution: {integrity: sha512-5i6TrGBRxG4vnfDpB6qSQGfnB6skGBXNL5/542w2uRGLimX6qeE5BQMLrzIC3JYV/xlGOv+s+hTleI9AZKUQNw==}
    engines: {node: '>= 12.13.0'}
    hasBin: true
    peerDependencies:
      webpack: ^4.37.0 || ^5.0.0
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack:
        optional: true
      webpack-cli:
        optional: true

  webpack-hot-middleware@2.25.3:
    resolution: {integrity: sha512-IK/0WAHs7MTu1tzLTjio73LjS3Ov+VvBKQmE8WPlJutgG5zT6Urgq/BbAdRrHTRpyzK0dvAvFh1Qg98akxgZpA==}

  webpack-manifest-plugin@5.0.0:
    resolution: {integrity: sha512-8RQfMAdc5Uw3QbCQ/CBV/AXqOR8mt03B6GJmRbhWopE8GzRfEpn+k0ZuWywxW+5QZsffhmFDY1J6ohqJo+eMuw==}
    engines: {node: '>=12.22.0'}
    peerDependencies:
      webpack: ^5.47.0

  webpack-merge@5.8.0:
    resolution: {integrity: sha512-/SaI7xY0831XwP6kzuwhKWVKDP9t1QY1h65lAFLbZqMPIuYcD9QAW4u9STIbU9kaJbPBB/geU/gLr1wDjOhQ+Q==}
    engines: {node: '>=10.0.0'}

  webpack-node-externals@3.0.0:
    resolution: {integrity: sha512-LnL6Z3GGDPht/AigwRh2dvL9PQPFQ8skEpVrWZXLWBYmqcaojHNN0onvHzie6rq7EWKrrBfPYqNEzTJgiwEQDQ==}
    engines: {node: '>=6'}

  webpack-sources@2.3.1:
    resolution: {integrity: sha512-y9EI9AO42JjEcrTJFOYmVywVZdKVUfOvDUPsJea5GIr1JOEGFVqwlY2K098fFoIjOkDzHn2AjRvM8dsBZu+gCA==}
    engines: {node: '>=10.13.0'}

  webpack-sources@3.2.3:
    resolution: {integrity: sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==}
    engines: {node: '>=10.13.0'}

  webpack@5.78.0:
    resolution: {integrity: sha512-gT5DP72KInmE/3azEaQrISjTvLYlSM0j1Ezhht/KLVkrqtv10JoP/RXhwmX/frrutOPuSq3o5Vq0ehR/4Vmd1g==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true

  webpackbar@5.0.2:
    resolution: {integrity: sha512-BmFJo7veBDgQzfWXl/wwYXr/VFus0614qZ8i9znqcl9fnEdiVkdbi0TedLQ6xAK92HZHDJ0QmyQ0fmuZPAgCYQ==}
    engines: {node: '>=12'}
    peerDependencies:
      webpack: 3 || 4 || 5

  websocket-driver@0.7.4:
    resolution: {integrity: sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg==}
    engines: {node: '>=0.8.0'}

  websocket-extensions@0.1.4:
    resolution: {integrity: sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg==}
    engines: {node: '>=0.8.0'}

  which-boxed-primitive@1.0.2:
    resolution: {integrity: sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==}

  which-typed-array@1.1.9:
    resolution: {integrity: sha512-w9c4xkx6mPidwp7180ckYWfMmvxpjlZuIudNtDf4N/tTAUB8VJbX25qZoAsrtGuYNnGw3pa0AXgbGKRB8/EceA==}
    engines: {node: '>= 0.4'}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  wildcard@2.0.0:
    resolution: {integrity: sha512-JcKqAHLPxcdb9KM49dufGXn2x3ssnfjbcaQdLlfZsL9rH9wgDQjUtDxbo8NE0F6SFvydeu1VhZe7hZuHsB2/pw==}

  word-wrap@1.2.3:
    resolution: {integrity: sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==}
    engines: {node: '>=0.10.0'}

  wrap-ansi@3.0.1:
    resolution: {integrity: sha512-iXR3tDXpbnTpzjKSylUJRkLuOrEC7hwEB221cgn6wtF8wpmz28puFXAEfPT5zrjM3wahygB//VuWEr1vTkDcNQ==}
    engines: {node: '>=4'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  ws@7.4.6:
    resolution: {integrity: sha512-YmhHDO4MzaDLB+M9ym/mDA5z0naX8j7SIlT8f8z+I0VtzsRbekxEutHSme7NPS2qE8StCYQNUnfWdXta/Yu85A==}
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  ws@7.5.9:
    resolution: {integrity: sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q==}
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  ws@8.13.0:
    resolution: {integrity: sha512-x9vcZYTrFPC7aSIbj7sRCYo7L/Xb8Iy+pW0ng0wt2vCJv7M9HOMy0UoN3rr+IFC7hb7vXoqS+P9ktyLLLhO+LA==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xdg-basedir@4.0.0:
    resolution: {integrity: sha512-PSNhEJDejZYV7h50BohL09Er9VaIefr2LMAf3OEmpCkjOi34eYyQYAXUTjEQtZJTKcF0E2UKTh+osDLsgNim9Q==}
    engines: {node: '>=8'}

  xml2js@0.5.0:
    resolution: {integrity: sha512-drPFnkQJik/O+uPKpqSgr22mpuFHqKdbS835iAQrUC73L2F5WkboIRd63ai/2Yg6I1jzifPFKH2NTK+cfglkIA==}
    engines: {node: '>=4.0.0'}

  xmlbuilder@11.0.1:
    resolution: {integrity: sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==}
    engines: {node: '>=4.0'}

  xregexp@2.0.0:
    resolution: {integrity: sha512-xl/50/Cf32VsGq/1R8jJE5ajH1yMCQkpmoS10QbFZWl2Oor4H0Me64Pu2yxvsRWK3m6soJbmGfzSR7BYmDcWAA==}

  y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}

  yaml@1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==}
    engines: {node: '>= 6'}

  yamljs@0.3.0:
    resolution: {integrity: sha512-C/FsVVhht4iPQYXOInoxUM/1ELSf9EsgKH34FofQOp6hwCPrW4vG4w5++TED3xRUo8gD7l0P1J1dLlDYzODsTQ==}
    hasBin: true

  yargs-parser@21.1.1:
    resolution: {integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==}
    engines: {node: '>=12'}

  yargs@17.7.1:
    resolution: {integrity: sha512-cwiTb08Xuv5fqF4AovYacTFNxk62th7LKJ6BL9IGUpTJrWoU7/7WdQGTP2SjKf1dUNBGzDd28p/Yfs/GI6JrLw==}
    engines: {node: '>=12'}

  ylru@1.3.2:
    resolution: {integrity: sha512-RXRJzMiK6U2ye0BlGGZnmpwJDPgakn6aNQ0A7gHRbD4I0uvK4TW6UqkK1V0pp9jskjJBAXd3dRrbzWkqJ+6cxA==}
    engines: {node: '>= 4.0.0'}

  yn@3.1.1:
    resolution: {integrity: sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==}
    engines: {node: '>=6'}

  yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}

snapshots:

  '@ampproject/remapping@2.2.1':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.3
      '@jridgewell/trace-mapping': 0.3.18

  '@aws-cdk/asset-awscli-v1@2.2.200': {}

  '@aws-cdk/asset-kubectl-v20@2.1.2': {}

  '@aws-cdk/asset-node-proxy-agent-v5@2.0.165': {}

  '@babel/code-frame@7.12.11':
    dependencies:
      '@babel/highlight': 7.18.6

  '@babel/code-frame@7.21.4':
    dependencies:
      '@babel/highlight': 7.18.6

  '@babel/compat-data@7.21.4': {}

  '@babel/core@7.21.4':
    dependencies:
      '@ampproject/remapping': 2.2.1
      '@babel/code-frame': 7.21.4
      '@babel/generator': 7.21.4
      '@babel/helper-compilation-targets': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-module-transforms': 7.21.2
      '@babel/helpers': 7.21.0
      '@babel/parser': 7.21.4
      '@babel/template': 7.20.7
      '@babel/traverse': 7.21.4(supports-color@5.5.0)
      '@babel/types': 7.21.4
      convert-source-map: 1.9.0
      debug: 4.3.4(supports-color@5.5.0)
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.21.4':
    dependencies:
      '@babel/types': 7.21.4
      '@jridgewell/gen-mapping': 0.3.3
      '@jridgewell/trace-mapping': 0.3.18
      jsesc: 2.5.2

  '@babel/helper-annotate-as-pure@7.18.6':
    dependencies:
      '@babel/types': 7.21.4

  '@babel/helper-builder-binary-assignment-operator-visitor@7.18.9':
    dependencies:
      '@babel/helper-explode-assignable-expression': 7.18.6
      '@babel/types': 7.21.4

  '@babel/helper-compilation-targets@7.21.4(@babel/core@7.21.4)':
    dependencies:
      '@babel/compat-data': 7.21.4
      '@babel/core': 7.21.4
      '@babel/helper-validator-option': 7.21.0
      browserslist: 4.21.5
      lru-cache: 5.1.1
      semver: 6.3.0

  '@babel/helper-create-class-features-plugin@7.21.4(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.21.0
      '@babel/helper-member-expression-to-functions': 7.21.0
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/helper-replace-supers': 7.20.7
      '@babel/helper-skip-transparent-expression-wrappers': 7.20.0
      '@babel/helper-split-export-declaration': 7.18.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-create-regexp-features-plugin@7.21.4(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-annotate-as-pure': 7.18.6
      regexpu-core: 5.3.2

  '@babel/helper-define-polyfill-provider@0.3.3(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-compilation-targets': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
      debug: 4.3.4(supports-color@5.5.0)
      lodash.debounce: 4.0.8
      resolve: 1.22.2
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-environment-visitor@7.18.9': {}

  '@babel/helper-explode-assignable-expression@7.18.6':
    dependencies:
      '@babel/types': 7.21.4

  '@babel/helper-function-name@7.21.0':
    dependencies:
      '@babel/template': 7.20.7
      '@babel/types': 7.21.4

  '@babel/helper-hoist-variables@7.18.6':
    dependencies:
      '@babel/types': 7.21.4

  '@babel/helper-member-expression-to-functions@7.21.0':
    dependencies:
      '@babel/types': 7.21.4

  '@babel/helper-module-imports@7.21.4':
    dependencies:
      '@babel/types': 7.21.4

  '@babel/helper-module-transforms@7.21.2':
    dependencies:
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-module-imports': 7.21.4
      '@babel/helper-simple-access': 7.20.2
      '@babel/helper-split-export-declaration': 7.18.6
      '@babel/helper-validator-identifier': 7.19.1
      '@babel/template': 7.20.7
      '@babel/traverse': 7.21.4(supports-color@5.5.0)
      '@babel/types': 7.21.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-optimise-call-expression@7.18.6':
    dependencies:
      '@babel/types': 7.21.4

  '@babel/helper-plugin-utils@7.20.2': {}

  '@babel/helper-remap-async-to-generator@7.18.9(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-wrap-function': 7.20.5
      '@babel/types': 7.21.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-replace-supers@7.20.7':
    dependencies:
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-member-expression-to-functions': 7.21.0
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/template': 7.20.7
      '@babel/traverse': 7.21.4(supports-color@5.5.0)
      '@babel/types': 7.21.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-simple-access@7.20.2':
    dependencies:
      '@babel/types': 7.21.4

  '@babel/helper-skip-transparent-expression-wrappers@7.20.0':
    dependencies:
      '@babel/types': 7.21.4

  '@babel/helper-split-export-declaration@7.18.6':
    dependencies:
      '@babel/types': 7.21.4

  '@babel/helper-string-parser@7.19.4': {}

  '@babel/helper-validator-identifier@7.19.1': {}

  '@babel/helper-validator-option@7.21.0': {}

  '@babel/helper-wrap-function@7.20.5':
    dependencies:
      '@babel/helper-function-name': 7.21.0
      '@babel/template': 7.20.7
      '@babel/traverse': 7.21.4(supports-color@5.5.0)
      '@babel/types': 7.21.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helpers@7.21.0':
    dependencies:
      '@babel/template': 7.20.7
      '@babel/traverse': 7.21.4(supports-color@5.5.0)
      '@babel/types': 7.21.4
    transitivePeerDependencies:
      - supports-color

  '@babel/highlight@7.18.6':
    dependencies:
      '@babel/helper-validator-identifier': 7.19.1
      chalk: 2.3.2
      js-tokens: 4.0.0

  '@babel/parser@7.21.4':
    dependencies:
      '@babel/types': 7.21.4

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.20.7(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-skip-transparent-expression-wrappers': 7.20.0
      '@babel/plugin-proposal-optional-chaining': 7.21.0(@babel/core@7.21.4)

  '@babel/plugin-proposal-async-generator-functions@7.20.7(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-remap-async-to-generator': 7.18.9(@babel/core@7.21.4)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.21.4)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-class-properties@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-create-class-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-class-static-block@7.21.0(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-create-class-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.21.4)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-dynamic-import@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.21.4)

  '@babel/plugin-proposal-export-namespace-from@7.18.9(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.21.4)

  '@babel/plugin-proposal-json-strings@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.21.4)

  '@babel/plugin-proposal-logical-assignment-operators@7.20.7(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.21.4)

  '@babel/plugin-proposal-nullish-coalescing-operator@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.21.4)

  '@babel/plugin-proposal-numeric-separator@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.21.4)

  '@babel/plugin-proposal-object-rest-spread@7.20.7(@babel/core@7.21.4)':
    dependencies:
      '@babel/compat-data': 7.21.4
      '@babel/core': 7.21.4
      '@babel/helper-compilation-targets': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.21.4)
      '@babel/plugin-transform-parameters': 7.21.3(@babel/core@7.21.4)

  '@babel/plugin-proposal-optional-catch-binding@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.21.4)

  '@babel/plugin-proposal-optional-chaining@7.21.0(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-skip-transparent-expression-wrappers': 7.20.0
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.21.4)

  '@babel/plugin-proposal-private-methods@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-create-class-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-private-property-in-object@7.21.0(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-create-class-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.21.4)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-unicode-property-regex@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-create-regexp-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-export-namespace-from@7.8.3(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-import-assertions@7.20.0(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-jsx@7.21.4(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-syntax-typescript@7.21.4(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-arrow-functions@7.20.7(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-async-to-generator@7.20.7(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-module-imports': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-remap-async-to-generator': 7.18.9(@babel/core@7.21.4)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-block-scoped-functions@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-block-scoping@7.21.0(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-classes@7.21.0(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-compilation-targets': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.21.0
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-replace-supers': 7.20.7
      '@babel/helper-split-export-declaration': 7.18.6
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-computed-properties@7.20.7(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/template': 7.20.7

  '@babel/plugin-transform-destructuring@7.21.3(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-dotall-regex@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-create-regexp-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-duplicate-keys@7.18.9(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-exponentiation-operator@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-builder-binary-assignment-operator-visitor': 7.18.9
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-for-of@7.21.0(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-function-name@7.18.9(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-compilation-targets': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-function-name': 7.21.0
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-literals@7.18.9(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-member-expression-literals@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-modules-amd@7.20.11(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-module-transforms': 7.21.2
      '@babel/helper-plugin-utils': 7.20.2
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-commonjs@7.21.2(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-module-transforms': 7.21.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-simple-access': 7.20.2
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-systemjs@7.20.11(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-hoist-variables': 7.18.6
      '@babel/helper-module-transforms': 7.21.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-validator-identifier': 7.19.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-umd@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-module-transforms': 7.21.2
      '@babel/helper-plugin-utils': 7.20.2
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-named-capturing-groups-regex@7.20.5(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-create-regexp-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-new-target@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-object-super@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-replace-supers': 7.20.7
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-parameters@7.21.3(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-property-literals@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-react-constant-elements@7.21.3(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-react-display-name@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-react-jsx-development@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/plugin-transform-react-jsx': 7.21.0(@babel/core@7.21.4)

  '@babel/plugin-transform-react-jsx@7.21.0(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-module-imports': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-jsx': 7.21.4(@babel/core@7.21.4)
      '@babel/types': 7.21.4

  '@babel/plugin-transform-react-pure-annotations@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-regenerator@7.20.5(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      regenerator-transform: 0.15.1

  '@babel/plugin-transform-reserved-words@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-shorthand-properties@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-spread@7.20.7(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-skip-transparent-expression-wrappers': 7.20.0

  '@babel/plugin-transform-sticky-regex@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-template-literals@7.18.9(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-typeof-symbol@7.18.9(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-typescript@7.21.3(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-create-class-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-typescript': 7.21.4(@babel/core@7.21.4)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-unicode-escapes@7.18.10(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/plugin-transform-unicode-regex@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-create-regexp-features-plugin': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2

  '@babel/preset-env@7.21.4(@babel/core@7.21.4)':
    dependencies:
      '@babel/compat-data': 7.21.4
      '@babel/core': 7.21.4
      '@babel/helper-compilation-targets': 7.21.4(@babel/core@7.21.4)
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-validator-option': 7.21.0
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.20.7(@babel/core@7.21.4)
      '@babel/plugin-proposal-async-generator-functions': 7.20.7(@babel/core@7.21.4)
      '@babel/plugin-proposal-class-properties': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-proposal-class-static-block': 7.21.0(@babel/core@7.21.4)
      '@babel/plugin-proposal-dynamic-import': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-proposal-export-namespace-from': 7.18.9(@babel/core@7.21.4)
      '@babel/plugin-proposal-json-strings': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-proposal-logical-assignment-operators': 7.20.7(@babel/core@7.21.4)
      '@babel/plugin-proposal-nullish-coalescing-operator': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-proposal-numeric-separator': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-proposal-object-rest-spread': 7.20.7(@babel/core@7.21.4)
      '@babel/plugin-proposal-optional-catch-binding': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-proposal-optional-chaining': 7.21.0(@babel/core@7.21.4)
      '@babel/plugin-proposal-private-methods': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-proposal-private-property-in-object': 7.21.0(@babel/core@7.21.4)
      '@babel/plugin-proposal-unicode-property-regex': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.21.4)
      '@babel/plugin-syntax-class-properties': 7.12.13(@babel/core@7.21.4)
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.21.4)
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.21.4)
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.21.4)
      '@babel/plugin-syntax-import-assertions': 7.20.0(@babel/core@7.21.4)
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.21.4)
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.21.4)
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.21.4)
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.21.4)
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.21.4)
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.21.4)
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.21.4)
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.21.4)
      '@babel/plugin-syntax-top-level-await': 7.14.5(@babel/core@7.21.4)
      '@babel/plugin-transform-arrow-functions': 7.20.7(@babel/core@7.21.4)
      '@babel/plugin-transform-async-to-generator': 7.20.7(@babel/core@7.21.4)
      '@babel/plugin-transform-block-scoped-functions': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-block-scoping': 7.21.0(@babel/core@7.21.4)
      '@babel/plugin-transform-classes': 7.21.0(@babel/core@7.21.4)
      '@babel/plugin-transform-computed-properties': 7.20.7(@babel/core@7.21.4)
      '@babel/plugin-transform-destructuring': 7.21.3(@babel/core@7.21.4)
      '@babel/plugin-transform-dotall-regex': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-duplicate-keys': 7.18.9(@babel/core@7.21.4)
      '@babel/plugin-transform-exponentiation-operator': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-for-of': 7.21.0(@babel/core@7.21.4)
      '@babel/plugin-transform-function-name': 7.18.9(@babel/core@7.21.4)
      '@babel/plugin-transform-literals': 7.18.9(@babel/core@7.21.4)
      '@babel/plugin-transform-member-expression-literals': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-modules-amd': 7.20.11(@babel/core@7.21.4)
      '@babel/plugin-transform-modules-commonjs': 7.21.2(@babel/core@7.21.4)
      '@babel/plugin-transform-modules-systemjs': 7.20.11(@babel/core@7.21.4)
      '@babel/plugin-transform-modules-umd': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-named-capturing-groups-regex': 7.20.5(@babel/core@7.21.4)
      '@babel/plugin-transform-new-target': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-object-super': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-parameters': 7.21.3(@babel/core@7.21.4)
      '@babel/plugin-transform-property-literals': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-regenerator': 7.20.5(@babel/core@7.21.4)
      '@babel/plugin-transform-reserved-words': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-shorthand-properties': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-spread': 7.20.7(@babel/core@7.21.4)
      '@babel/plugin-transform-sticky-regex': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-template-literals': 7.18.9(@babel/core@7.21.4)
      '@babel/plugin-transform-typeof-symbol': 7.18.9(@babel/core@7.21.4)
      '@babel/plugin-transform-unicode-escapes': 7.18.10(@babel/core@7.21.4)
      '@babel/plugin-transform-unicode-regex': 7.18.6(@babel/core@7.21.4)
      '@babel/preset-modules': 0.1.5(@babel/core@7.21.4)
      '@babel/types': 7.21.4
      babel-plugin-polyfill-corejs2: 0.3.3(@babel/core@7.21.4)
      babel-plugin-polyfill-corejs3: 0.6.0(@babel/core@7.21.4)
      babel-plugin-polyfill-regenerator: 0.4.1(@babel/core@7.21.4)
      core-js-compat: 3.30.0
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color

  '@babel/preset-modules@0.1.5(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-proposal-unicode-property-regex': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-dotall-regex': 7.18.6(@babel/core@7.21.4)
      '@babel/types': 7.21.4
      esutils: 2.0.3

  '@babel/preset-react@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-validator-option': 7.21.0
      '@babel/plugin-transform-react-display-name': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-react-jsx': 7.21.0(@babel/core@7.21.4)
      '@babel/plugin-transform-react-jsx-development': 7.18.6(@babel/core@7.21.4)
      '@babel/plugin-transform-react-pure-annotations': 7.18.6(@babel/core@7.21.4)

  '@babel/preset-typescript@7.18.6(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-validator-option': 7.21.0
      '@babel/plugin-transform-typescript': 7.21.3(@babel/core@7.21.4)
    transitivePeerDependencies:
      - supports-color

  '@babel/regjsgen@0.8.0': {}

  '@babel/runtime@7.21.0':
    dependencies:
      regenerator-runtime: 0.13.11

  '@babel/template@7.20.7':
    dependencies:
      '@babel/code-frame': 7.21.4
      '@babel/parser': 7.21.4
      '@babel/types': 7.21.4

  '@babel/traverse@7.21.4(supports-color@5.5.0)':
    dependencies:
      '@babel/code-frame': 7.21.4
      '@babel/generator': 7.21.4
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.21.0
      '@babel/helper-hoist-variables': 7.18.6
      '@babel/helper-split-export-declaration': 7.18.6
      '@babel/parser': 7.21.4
      '@babel/types': 7.21.4
      debug: 4.3.4(supports-color@5.5.0)
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.21.4':
    dependencies:
      '@babel/helper-string-parser': 7.19.4
      '@babel/helper-validator-identifier': 7.19.1
      to-fast-properties: 2.0.0

  '@balena/dockerignore@1.0.2': {}

  '@cspotcode/source-map-support@0.8.1':
    dependencies:
      '@jridgewell/trace-mapping': 0.3.9

  '@csstools/postcss-cascade-layers@1.1.1(postcss@8.4.38)':
    dependencies:
      '@csstools/selector-specificity': 2.2.0(postcss-selector-parser@6.0.11)
      postcss: 8.4.38
      postcss-selector-parser: 6.0.11

  '@csstools/postcss-color-function@1.1.1(postcss@8.4.38)':
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0(postcss@8.4.38)
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  '@csstools/postcss-font-format-keywords@1.0.1(postcss@8.4.38)':
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  '@csstools/postcss-hwb-function@1.0.2(postcss@8.4.38)':
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  '@csstools/postcss-ic-unit@1.0.1(postcss@8.4.38)':
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0(postcss@8.4.38)
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  '@csstools/postcss-is-pseudo-class@2.0.7(postcss@8.4.38)':
    dependencies:
      '@csstools/selector-specificity': 2.2.0(postcss-selector-parser@6.0.11)
      postcss: 8.4.38
      postcss-selector-parser: 6.0.11

  '@csstools/postcss-nested-calc@1.0.0(postcss@8.4.38)':
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  '@csstools/postcss-normalize-display-values@1.0.1(postcss@8.4.38)':
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  '@csstools/postcss-oklab-function@1.1.1(postcss@8.4.38)':
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0(postcss@8.4.38)
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  '@csstools/postcss-progressive-custom-properties@1.3.0(postcss@8.4.38)':
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  '@csstools/postcss-stepped-value-functions@1.0.1(postcss@8.4.38)':
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  '@csstools/postcss-text-decoration-shorthand@1.0.0(postcss@8.4.38)':
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  '@csstools/postcss-trigonometric-functions@1.0.2(postcss@8.4.38)':
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  '@csstools/postcss-unset-value@1.0.2(postcss@8.4.38)':
    dependencies:
      postcss: 8.4.38

  '@csstools/selector-specificity@2.2.0(postcss-selector-parser@6.0.11)':
    dependencies:
      postcss-selector-parser: 6.0.11

  '@discoveryjs/json-ext@0.5.7': {}

  '@emotion/is-prop-valid@1.2.0':
    dependencies:
      '@emotion/memoize': 0.8.0

  '@emotion/memoize@0.8.0': {}

  '@emotion/stylis@0.8.5': {}

  '@emotion/unitless@0.7.5': {}

  '@eslint-community/eslint-utils@4.4.0(eslint@8.38.0)':
    dependencies:
      eslint: 8.38.0
      eslint-visitor-keys: 3.4.0

  '@eslint-community/regexpp@4.5.0': {}

  '@eslint/eslintrc@0.4.3':
    dependencies:
      ajv: 6.12.6
      debug: 4.3.4(supports-color@5.5.0)
      espree: 7.3.1
      globals: 13.20.0
      ignore: 4.0.6
      import-fresh: 3.3.0
      js-yaml: 3.14.1
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/eslintrc@2.0.2':
    dependencies:
      ajv: 6.12.6
      debug: 4.3.4(supports-color@5.5.0)
      espree: 9.5.1
      globals: 13.20.0
      ignore: 5.2.4
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@8.38.0': {}

  '@formatjs/ecma402-abstract@1.14.3':
    dependencies:
      '@formatjs/intl-localematcher': 0.2.32
      tslib: 2.5.0

  '@formatjs/fast-memoize@2.0.1':
    dependencies:
      tslib: 2.5.0

  '@formatjs/icu-messageformat-parser@2.3.1':
    dependencies:
      '@formatjs/ecma402-abstract': 1.14.3
      '@formatjs/icu-skeleton-parser': 1.3.18
      tslib: 2.5.0

  '@formatjs/icu-skeleton-parser@1.3.18':
    dependencies:
      '@formatjs/ecma402-abstract': 1.14.3
      tslib: 2.5.0

  '@formatjs/intl-displaynames@6.3.0':
    dependencies:
      '@formatjs/ecma402-abstract': 1.14.3
      '@formatjs/intl-localematcher': 0.2.32
      tslib: 2.5.0

  '@formatjs/intl-listformat@7.2.0':
    dependencies:
      '@formatjs/ecma402-abstract': 1.14.3
      '@formatjs/intl-localematcher': 0.2.32
      tslib: 2.5.0

  '@formatjs/intl-localematcher@0.2.32':
    dependencies:
      tslib: 2.5.0

  '@formatjs/intl@2.7.0(typescript@5.0.3)':
    dependencies:
      '@formatjs/ecma402-abstract': 1.14.3
      '@formatjs/fast-memoize': 2.0.1
      '@formatjs/icu-messageformat-parser': 2.3.1
      '@formatjs/intl-displaynames': 6.3.0
      '@formatjs/intl-listformat': 7.2.0
      intl-messageformat: 10.3.4
      tslib: 2.5.0
      typescript: 5.0.3

  '@humanwhocodes/config-array@0.11.8':
    dependencies:
      '@humanwhocodes/object-schema': 1.2.1
      debug: 4.3.4(supports-color@5.5.0)
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@humanwhocodes/config-array@0.5.0':
    dependencies:
      '@humanwhocodes/object-schema': 1.2.1
      debug: 4.3.4(supports-color@5.5.0)
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/object-schema@1.2.1': {}

  '@jest/schemas@29.4.3':
    dependencies:
      '@sinclair/typebox': 0.25.24

  '@jest/types@29.5.0':
    dependencies:
      '@jest/schemas': 29.4.3
      '@types/istanbul-lib-coverage': 2.0.4
      '@types/istanbul-reports': 3.0.1
      '@types/node': 18.15.11
      '@types/yargs': 17.0.24
      chalk: 4.1.2

  '@jridgewell/gen-mapping@0.3.3':
    dependencies:
      '@jridgewell/set-array': 1.1.2
      '@jridgewell/sourcemap-codec': 1.4.15
      '@jridgewell/trace-mapping': 0.3.18

  '@jridgewell/resolve-uri@3.1.0': {}

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.1.2': {}

  '@jridgewell/source-map@0.3.3':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.3
      '@jridgewell/trace-mapping': 0.3.18

  '@jridgewell/sourcemap-codec@1.4.14': {}

  '@jridgewell/sourcemap-codec@1.4.15': {}

  '@jridgewell/trace-mapping@0.3.18':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.0
      '@jridgewell/sourcemap-codec': 1.4.14

  '@jridgewell/trace-mapping@0.3.9':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.4.15

  '@koa/router@12.0.0':
    dependencies:
      http-errors: 2.0.0
      koa-compose: 4.1.0
      methods: 1.1.2
      path-to-regexp: 6.2.1

  '@leichtgewicht/ip-codec@2.0.4': {}

  '@loadable/babel-plugin@5.15.3(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.21.4)

  '@loadable/component@5.15.3(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.21.0
      hoist-non-react-statics: 3.3.2
      react: 18.3.1
      react-is: 16.13.1

  '@loadable/server@5.15.3(@loadable/component@5.15.3)(react@18.3.1)':
    dependencies:
      '@loadable/component': 5.15.3(react@18.3.1)
      lodash: 4.17.21
      react: 18.3.1

  '@loadable/webpack-plugin@5.15.2(webpack@5.78.0)':
    dependencies:
      make-dir: 3.1.0
      webpack: 5.78.0(webpack-cli@5.0.1)

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.15.0

  '@opencensus/core@0.0.8':
    dependencies:
      continuation-local-storage: 3.2.1
      log-driver: 1.2.7
      semver: 5.7.1
      shimmer: 1.2.1
      uuid: 3.4.0

  '@opencensus/core@0.0.9':
    dependencies:
      continuation-local-storage: 3.2.1
      log-driver: 1.2.7
      semver: 5.7.1
      shimmer: 1.2.1
      uuid: 3.4.0

  '@opencensus/propagation-b3@0.0.8':
    dependencies:
      '@opencensus/core': 0.0.8
      uuid: 3.4.0

  '@pm2/agent@2.0.1':
    dependencies:
      async: 3.2.4
      chalk: 3.0.0
      dayjs: 1.8.36
      debug: 4.3.4(supports-color@5.5.0)
      eventemitter2: 5.0.1
      fast-json-patch: 3.1.1
      fclone: 1.0.11
      nssocket: 0.6.0
      pm2-axon: 4.0.1
      pm2-axon-rpc: 0.7.1
      proxy-agent: 5.0.0
      semver: 7.2.3
      ws: 7.4.6
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  '@pm2/io@5.0.0':
    dependencies:
      '@opencensus/core': 0.0.9
      '@opencensus/propagation-b3': 0.0.8
      async: 2.6.4
      debug: 4.3.4(supports-color@5.5.0)
      eventemitter2: 6.4.9
      require-in-the-middle: 5.2.0
      semver: 6.3.0
      shimmer: 1.2.1
      signal-exit: 3.0.7
      tslib: 1.9.3
    transitivePeerDependencies:
      - supports-color

  '@pm2/js-api@0.6.7':
    dependencies:
      async: 2.6.4
      axios: 0.21.4(debug@4.3.4)
      debug: 4.3.4(supports-color@5.5.0)
      eventemitter2: 6.4.9
      ws: 7.5.9
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  '@pm2/pm2-version-check@1.0.4':
    dependencies:
      debug: 4.3.4(supports-color@5.5.0)
    transitivePeerDependencies:
      - supports-color

  '@pmmmwh/react-refresh-webpack-plugin@0.5.10(react-refresh@0.14.0)(webpack-dev-server@4.13.2)(webpack-hot-middleware@2.25.3)(webpack@5.78.0)':
    dependencies:
      ansi-html-community: 0.0.8
      common-path-prefix: 3.0.0
      core-js-pure: 3.30.0
      error-stack-parser: 2.1.4
      find-up: 5.0.0
      html-entities: 2.3.3
      loader-utils: 2.0.4
      react-refresh: 0.14.0
      schema-utils: 3.1.1
      source-map: 0.7.4
      webpack: 5.78.0(webpack-cli@5.0.1)
      webpack-dev-server: 4.13.2(webpack-cli@5.0.1)(webpack@5.78.0)
      webpack-hot-middleware: 2.25.3

  '@polka/url@1.0.0-next.21': {}

  '@principalstudio/html-webpack-inject-preload@1.2.7(html-webpack-plugin@5.5.0)(webpack@5.78.0)':
    dependencies:
      html-webpack-plugin: 5.5.0(webpack@5.78.0)
      webpack: 5.78.0(webpack-cli@5.0.1)

  '@remix-run/router@1.5.0': {}

  '@sinclair/typebox@0.25.24': {}

  '@svgr/babel-plugin-add-jsx-attribute@6.5.1(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4

  '@svgr/babel-plugin-remove-jsx-attribute@7.0.0(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4

  '@svgr/babel-plugin-remove-jsx-empty-expression@7.0.0(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4

  '@svgr/babel-plugin-replace-jsx-attribute-value@6.5.1(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4

  '@svgr/babel-plugin-svg-dynamic-title@6.5.1(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4

  '@svgr/babel-plugin-svg-em-dimensions@6.5.1(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4

  '@svgr/babel-plugin-transform-react-native-svg@6.5.1(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4

  '@svgr/babel-plugin-transform-svg-component@6.5.1(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4

  '@svgr/babel-preset@6.5.1(@babel/core@7.21.4)':
    dependencies:
      '@babel/core': 7.21.4
      '@svgr/babel-plugin-add-jsx-attribute': 6.5.1(@babel/core@7.21.4)
      '@svgr/babel-plugin-remove-jsx-attribute': 7.0.0(@babel/core@7.21.4)
      '@svgr/babel-plugin-remove-jsx-empty-expression': 7.0.0(@babel/core@7.21.4)
      '@svgr/babel-plugin-replace-jsx-attribute-value': 6.5.1(@babel/core@7.21.4)
      '@svgr/babel-plugin-svg-dynamic-title': 6.5.1(@babel/core@7.21.4)
      '@svgr/babel-plugin-svg-em-dimensions': 6.5.1(@babel/core@7.21.4)
      '@svgr/babel-plugin-transform-react-native-svg': 6.5.1(@babel/core@7.21.4)
      '@svgr/babel-plugin-transform-svg-component': 6.5.1(@babel/core@7.21.4)

  '@svgr/core@6.5.1':
    dependencies:
      '@babel/core': 7.21.4
      '@svgr/babel-preset': 6.5.1(@babel/core@7.21.4)
      '@svgr/plugin-jsx': 6.5.1(@svgr/core@6.5.1)
      camelcase: 6.3.0
      cosmiconfig: 7.1.0
    transitivePeerDependencies:
      - supports-color

  '@svgr/hast-util-to-babel-ast@6.5.1':
    dependencies:
      '@babel/types': 7.21.4
      entities: 4.4.0

  '@svgr/plugin-jsx@6.5.1(@svgr/core@6.5.1)':
    dependencies:
      '@babel/core': 7.21.4
      '@svgr/babel-preset': 6.5.1(@babel/core@7.21.4)
      '@svgr/core': 6.5.1
      '@svgr/hast-util-to-babel-ast': 6.5.1
      svg-parser: 2.0.4
    transitivePeerDependencies:
      - supports-color

  '@svgr/plugin-svgo@6.5.1(@svgr/core@6.5.1)':
    dependencies:
      '@svgr/core': 6.5.1
      cosmiconfig: 7.1.0
      deepmerge: 4.3.1
      svgo: 2.8.0

  '@svgr/webpack@6.5.1':
    dependencies:
      '@babel/core': 7.21.4
      '@babel/plugin-transform-react-constant-elements': 7.21.3(@babel/core@7.21.4)
      '@babel/preset-env': 7.21.4(@babel/core@7.21.4)
      '@babel/preset-react': 7.18.6(@babel/core@7.21.4)
      '@babel/preset-typescript': 7.18.6(@babel/core@7.21.4)
      '@svgr/core': 6.5.1
      '@svgr/plugin-jsx': 6.5.1(@svgr/core@6.5.1)
      '@svgr/plugin-svgo': 6.5.1(@svgr/core@6.5.1)
    transitivePeerDependencies:
      - supports-color

  '@tanstack/query-core@4.29.1': {}

  '@tanstack/react-query@4.29.3(react-dom@18.3.1)(react@18.3.1)':
    dependencies:
      '@tanstack/query-core': 4.29.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      use-sync-external-store: 1.2.0(react@18.3.1)

  '@tootallnate/once@1.1.2': {}

  '@trysound/sax@0.2.0': {}

  '@tsconfig/node10@1.0.11': {}

  '@tsconfig/node12@1.0.11': {}

  '@tsconfig/node14@1.0.3': {}

  '@tsconfig/node16@1.0.4': {}

  '@types/accepts@1.3.5':
    dependencies:
      '@types/node': 18.15.11

  '@types/aws-lambda@8.10.119': {}

  '@types/body-parser@1.19.2':
    dependencies:
      '@types/connect': 3.4.35
      '@types/node': 18.15.11

  '@types/bonjour@3.5.10':
    dependencies:
      '@types/node': 18.15.11

  '@types/connect-history-api-fallback@1.3.5':
    dependencies:
      '@types/express-serve-static-core': 4.17.33
      '@types/node': 18.15.11

  '@types/connect@3.4.35':
    dependencies:
      '@types/node': 18.15.11

  '@types/content-disposition@0.5.5': {}

  '@types/cookies@0.7.7':
    dependencies:
      '@types/connect': 3.4.35
      '@types/express': 4.17.17
      '@types/keygrip': 1.0.2
      '@types/node': 18.15.11

  '@types/eslint-scope@3.7.4':
    dependencies:
      '@types/eslint': 8.37.0
      '@types/estree': 0.0.51

  '@types/eslint@8.37.0':
    dependencies:
      '@types/estree': 1.0.0
      '@types/json-schema': 7.0.11

  '@types/estree@0.0.51': {}

  '@types/estree@1.0.0': {}

  '@types/express-serve-static-core@4.17.33':
    dependencies:
      '@types/node': 18.15.11
      '@types/qs': 6.9.7
      '@types/range-parser': 1.2.4

  '@types/express@4.17.17':
    dependencies:
      '@types/body-parser': 1.19.2
      '@types/express-serve-static-core': 4.17.33
      '@types/qs': 6.9.7
      '@types/serve-static': 1.15.1

  '@types/hoist-non-react-statics@3.3.1':
    dependencies:
      '@types/react': 18.3.2
      hoist-non-react-statics: 3.3.2

  '@types/html-minifier-terser@6.1.0': {}

  '@types/http-assert@1.5.3': {}

  '@types/http-errors@2.0.1': {}

  '@types/http-proxy@1.17.10':
    dependencies:
      '@types/node': 18.15.11

  '@types/istanbul-lib-coverage@2.0.4': {}

  '@types/istanbul-lib-report@3.0.0':
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.4

  '@types/istanbul-reports@3.0.1':
    dependencies:
      '@types/istanbul-lib-report': 3.0.0

  '@types/json-schema@7.0.11': {}

  '@types/json5@0.0.29': {}

  '@types/keygrip@1.0.2': {}

  '@types/koa-compose@3.2.5':
    dependencies:
      '@types/koa': 2.13.6

  '@types/koa-mount@4.0.2':
    dependencies:
      '@types/koa': 2.13.6

  '@types/koa-send@4.1.3':
    dependencies:
      '@types/koa': 2.13.6

  '@types/koa-static@4.0.2':
    dependencies:
      '@types/koa': 2.13.6
      '@types/koa-send': 4.1.3

  '@types/koa@2.13.6':
    dependencies:
      '@types/accepts': 1.3.5
      '@types/content-disposition': 0.5.5
      '@types/cookies': 0.7.7
      '@types/http-assert': 1.5.3
      '@types/http-errors': 2.0.1
      '@types/keygrip': 1.0.2
      '@types/koa-compose': 3.2.5
      '@types/node': 18.15.11

  '@types/koa__router@12.0.0':
    dependencies:
      '@types/koa': 2.13.6

  '@types/loadable__component@5.13.4':
    dependencies:
      '@types/react': 18.3.2

  '@types/loadable__server@5.12.6':
    dependencies:
      '@types/react': 18.3.2

  '@types/mime@3.0.1': {}

  '@types/node@18.15.11': {}

  '@types/parse-json@4.0.0': {}

  '@types/prop-types@15.7.5': {}

  '@types/qs@6.9.7': {}

  '@types/range-parser@1.2.4': {}

  '@types/react-dom@18.3.0':
    dependencies:
      '@types/react': 18.3.2

  '@types/react@18.3.2':
    dependencies:
      '@types/prop-types': 15.7.5
      csstype: 3.1.2

  '@types/retry@0.12.0': {}

  '@types/serve-index@1.9.1':
    dependencies:
      '@types/express': 4.17.17

  '@types/serve-static@1.15.1':
    dependencies:
      '@types/mime': 3.0.1
      '@types/node': 18.15.11

  '@types/sockjs@0.3.33':
    dependencies:
      '@types/node': 18.15.11

  '@types/styled-components@5.1.26':
    dependencies:
      '@types/hoist-non-react-statics': 3.3.1
      '@types/react': 18.3.2
      csstype: 3.1.2

  '@types/webpack-dev-middleware@5.3.0(webpack@5.78.0)':
    dependencies:
      webpack-dev-middleware: 6.0.2(webpack@5.78.0)
    transitivePeerDependencies:
      - webpack

  '@types/webpack-hot-middleware@2.25.6(webpack-cli@5.0.1)':
    dependencies:
      '@types/connect': 3.4.35
      tapable: 2.2.1
      webpack: 5.78.0(webpack-cli@5.0.1)
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js
      - webpack-cli

  '@types/ws@8.5.4':
    dependencies:
      '@types/node': 18.15.11

  '@types/yargs-parser@21.0.0': {}

  '@types/yargs@17.0.24':
    dependencies:
      '@types/yargs-parser': 21.0.0

  '@vendia/serverless-express@4.10.4': {}

  '@webassemblyjs/ast@1.11.1':
    dependencies:
      '@webassemblyjs/helper-numbers': 1.11.1
      '@webassemblyjs/helper-wasm-bytecode': 1.11.1

  '@webassemblyjs/floating-point-hex-parser@1.11.1': {}

  '@webassemblyjs/helper-api-error@1.11.1': {}

  '@webassemblyjs/helper-buffer@1.11.1': {}

  '@webassemblyjs/helper-numbers@1.11.1':
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': 1.11.1
      '@webassemblyjs/helper-api-error': 1.11.1
      '@xtuc/long': 4.2.2

  '@webassemblyjs/helper-wasm-bytecode@1.11.1': {}

  '@webassemblyjs/helper-wasm-section@1.11.1':
    dependencies:
      '@webassemblyjs/ast': 1.11.1
      '@webassemblyjs/helper-buffer': 1.11.1
      '@webassemblyjs/helper-wasm-bytecode': 1.11.1
      '@webassemblyjs/wasm-gen': 1.11.1

  '@webassemblyjs/ieee754@1.11.1':
    dependencies:
      '@xtuc/ieee754': 1.2.0

  '@webassemblyjs/leb128@1.11.1':
    dependencies:
      '@xtuc/long': 4.2.2

  '@webassemblyjs/utf8@1.11.1': {}

  '@webassemblyjs/wasm-edit@1.11.1':
    dependencies:
      '@webassemblyjs/ast': 1.11.1
      '@webassemblyjs/helper-buffer': 1.11.1
      '@webassemblyjs/helper-wasm-bytecode': 1.11.1
      '@webassemblyjs/helper-wasm-section': 1.11.1
      '@webassemblyjs/wasm-gen': 1.11.1
      '@webassemblyjs/wasm-opt': 1.11.1
      '@webassemblyjs/wasm-parser': 1.11.1
      '@webassemblyjs/wast-printer': 1.11.1

  '@webassemblyjs/wasm-gen@1.11.1':
    dependencies:
      '@webassemblyjs/ast': 1.11.1
      '@webassemblyjs/helper-wasm-bytecode': 1.11.1
      '@webassemblyjs/ieee754': 1.11.1
      '@webassemblyjs/leb128': 1.11.1
      '@webassemblyjs/utf8': 1.11.1

  '@webassemblyjs/wasm-opt@1.11.1':
    dependencies:
      '@webassemblyjs/ast': 1.11.1
      '@webassemblyjs/helper-buffer': 1.11.1
      '@webassemblyjs/wasm-gen': 1.11.1
      '@webassemblyjs/wasm-parser': 1.11.1

  '@webassemblyjs/wasm-parser@1.11.1':
    dependencies:
      '@webassemblyjs/ast': 1.11.1
      '@webassemblyjs/helper-api-error': 1.11.1
      '@webassemblyjs/helper-wasm-bytecode': 1.11.1
      '@webassemblyjs/ieee754': 1.11.1
      '@webassemblyjs/leb128': 1.11.1
      '@webassemblyjs/utf8': 1.11.1

  '@webassemblyjs/wast-printer@1.11.1':
    dependencies:
      '@webassemblyjs/ast': 1.11.1
      '@xtuc/long': 4.2.2

  '@webpack-cli/configtest@2.0.1(webpack-cli@5.0.1)(webpack@5.78.0)':
    dependencies:
      webpack: 5.78.0(webpack-cli@5.0.1)
      webpack-cli: 5.0.1(webpack-bundle-analyzer@4.8.0)(webpack-dev-server@4.13.2)(webpack@5.78.0)

  '@webpack-cli/info@2.0.1(webpack-cli@5.0.1)(webpack@5.78.0)':
    dependencies:
      webpack: 5.78.0(webpack-cli@5.0.1)
      webpack-cli: 5.0.1(webpack-bundle-analyzer@4.8.0)(webpack-dev-server@4.13.2)(webpack@5.78.0)

  '@webpack-cli/serve@2.0.1(webpack-cli@5.0.1)(webpack-dev-server@4.13.2)(webpack@5.78.0)':
    dependencies:
      webpack: 5.78.0(webpack-cli@5.0.1)
      webpack-cli: 5.0.1(webpack-bundle-analyzer@4.8.0)(webpack-dev-server@4.13.2)(webpack@5.78.0)
      webpack-dev-server: 4.13.2(webpack-cli@5.0.1)(webpack@5.78.0)

  '@xtuc/ieee754@1.2.0': {}

  '@xtuc/long@4.2.2': {}

  abbrev@1.1.1: {}

  accepts@1.3.8:
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  acorn-import-assertions@1.8.0(acorn@8.8.2):
    dependencies:
      acorn: 8.8.2

  acorn-jsx@5.3.2(acorn@7.4.1):
    dependencies:
      acorn: 7.4.1

  acorn-jsx@5.3.2(acorn@8.8.2):
    dependencies:
      acorn: 8.8.2

  acorn-walk@8.2.0: {}

  acorn-walk@8.3.2: {}

  acorn@7.4.1: {}

  acorn@8.11.3: {}

  acorn@8.8.2: {}

  agent-base@6.0.2:
    dependencies:
      debug: 4.3.4(supports-color@5.5.0)
    transitivePeerDependencies:
      - supports-color

  ajv-formats@2.1.1(ajv@8.12.0):
    dependencies:
      ajv: 8.12.0

  ajv-keywords@3.5.2(ajv@6.12.6):
    dependencies:
      ajv: 6.12.6

  ajv-keywords@5.1.0(ajv@8.12.0):
    dependencies:
      ajv: 8.12.0
      fast-deep-equal: 3.1.3

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.12.0:
    dependencies:
      fast-deep-equal: 3.1.3
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
      uri-js: 4.4.1

  amp-message@0.1.2:
    dependencies:
      amp: 0.3.1

  amp@0.3.1: {}

  ansi-colors@4.1.3: {}

  ansi-escapes@3.2.0: {}

  ansi-html-community@0.0.8: {}

  ansi-regex@2.1.1: {}

  ansi-regex@3.0.1: {}

  ansi-regex@5.0.1: {}

  ansi-styles@2.2.1: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  arg@4.1.3: {}

  arg@5.0.2: {}

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  argparse@2.0.1: {}

  array-buffer-byte-length@1.0.0:
    dependencies:
      call-bind: 1.0.2
      is-array-buffer: 3.0.2

  array-flatten@1.1.1: {}

  array-flatten@2.1.2: {}

  array-includes@3.1.6:
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2
      get-intrinsic: 1.2.0
      is-string: 1.0.7

  array.prototype.flat@1.3.1:
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2
      es-shim-unscopables: 1.0.0

  array.prototype.flatmap@1.3.1:
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2
      es-shim-unscopables: 1.0.0

  array.prototype.tosorted@1.1.1:
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2
      es-shim-unscopables: 1.0.0
      get-intrinsic: 1.2.0

  ast-types@0.13.4:
    dependencies:
      tslib: 2.5.0

  astral-regex@2.0.0: {}

  async-listener@0.6.10:
    dependencies:
      semver: 5.7.1
      shimmer: 1.2.1

  async@2.6.4:
    dependencies:
      lodash: 4.17.21

  async@3.2.4: {}

  asynckit@0.4.0: {}

  autoprefixer@10.4.14(postcss@8.4.38):
    dependencies:
      browserslist: 4.21.5
      caniuse-lite: 1.0.30001617
      fraction.js: 4.2.0
      normalize-range: 0.1.2
      picocolors: 1.0.0
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  available-typed-arrays@1.0.5: {}

  aws-cdk-lib@2.86.0(constructs@10.2.69):
    dependencies:
      '@aws-cdk/asset-awscli-v1': 2.2.200
      '@aws-cdk/asset-kubectl-v20': 2.1.2
      '@aws-cdk/asset-node-proxy-agent-v5': 2.0.165
      '@balena/dockerignore': 1.0.2
      case: 1.6.3
      constructs: 10.2.69
      fs-extra: 11.1.1
      ignore: 5.2.4
      jsonschema: 1.4.1
      minimatch: 3.1.2
      punycode: 2.3.0
      semver: 7.5.3
      table: 6.8.1
      yaml: 1.10.2

  aws-lambda@1.0.7:
    dependencies:
      aws-sdk: 2.1411.0
      commander: 3.0.2
      js-yaml: 3.14.1
      watchpack: 2.4.0

  aws-sdk@2.1411.0:
    dependencies:
      buffer: 4.9.2
      events: 1.1.1
      ieee754: 1.1.13
      jmespath: 0.16.0
      querystring: 0.2.0
      sax: 1.2.1
      url: 0.10.3
      util: 0.12.5
      uuid: 8.0.0
      xml2js: 0.5.0

  axios@0.21.4(debug@4.3.4):
    dependencies:
      follow-redirects: 1.15.2(debug@4.3.4)
    transitivePeerDependencies:
      - debug

  axios@1.3.6:
    dependencies:
      follow-redirects: 1.15.2(debug@4.3.4)
      form-data: 4.0.0
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  babel-loader@9.1.2(@babel/core@7.21.4)(webpack@5.78.0):
    dependencies:
      '@babel/core': 7.21.4
      find-cache-dir: 3.3.2
      schema-utils: 4.0.0
      webpack: 5.78.0(webpack-cli@5.0.1)

  babel-plugin-polyfill-corejs2@0.3.3(@babel/core@7.21.4):
    dependencies:
      '@babel/compat-data': 7.21.4
      '@babel/core': 7.21.4
      '@babel/helper-define-polyfill-provider': 0.3.3(@babel/core@7.21.4)
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs3@0.6.0(@babel/core@7.21.4):
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-define-polyfill-provider': 0.3.3(@babel/core@7.21.4)
      core-js-compat: 3.30.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-regenerator@0.4.1(@babel/core@7.21.4):
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-define-polyfill-provider': 0.3.3(@babel/core@7.21.4)
    transitivePeerDependencies:
      - supports-color

  babel-plugin-styled-components@2.1.1(styled-components@5.3.9):
    dependencies:
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-module-imports': 7.21.4
      babel-plugin-syntax-jsx: 6.18.0
      lodash: 4.17.21
      picomatch: 2.3.1
      styled-components: 5.3.9(react-dom@18.3.1)(react-is@18.2.0)(react@18.3.1)

  babel-plugin-syntax-jsx@6.18.0: {}

  babel-plugin-transform-commonjs@1.1.6(@babel/core@7.21.4):
    dependencies:
      '@babel/core': 7.21.4
      '@babel/helper-plugin-utils': 7.20.2

  balanced-match@1.0.2: {}

  base64-js@1.5.1: {}

  basic-auth@2.0.1:
    dependencies:
      safe-buffer: 5.1.2

  batch@0.6.1: {}

  big.js@5.2.2: {}

  binary-extensions@2.2.0: {}

  blessed@0.1.81: {}

  bodec@0.1.0: {}

  body-parser@1.20.1:
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.11.0
      raw-body: 2.5.1
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  bonjour-service@1.1.1:
    dependencies:
      array-flatten: 2.1.2
      dns-equal: 1.0.0
      fast-deep-equal: 3.1.3
      multicast-dns: 7.2.5

  boolbase@1.0.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  braces@3.0.2:
    dependencies:
      fill-range: 7.0.1

  browserslist@4.21.5:
    dependencies:
      caniuse-lite: 1.0.30001617
      electron-to-chromium: 1.4.355
      node-releases: 2.0.10
      update-browserslist-db: 1.0.10(browserslist@4.21.5)

  buffer-from@1.1.2: {}

  buffer@4.9.2:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.1.13
      isarray: 1.0.0

  builtins@5.0.1:
    dependencies:
      semver: 7.5.3

  bytes@3.0.0: {}

  bytes@3.1.2: {}

  cache-content-type@1.0.1:
    dependencies:
      mime-types: 2.1.35
      ylru: 1.3.2

  call-bind@1.0.2:
    dependencies:
      function-bind: 1.1.1
      get-intrinsic: 1.2.0

  callsites@3.1.0: {}

  camel-case@4.1.2:
    dependencies:
      pascal-case: 3.1.2
      tslib: 2.5.0

  camelcase-css@2.0.1: {}

  camelcase@6.3.0: {}

  camelize@1.0.1: {}

  caniuse-api@3.0.0:
    dependencies:
      browserslist: 4.21.5
      caniuse-lite: 1.0.30001617
      lodash.memoize: 4.1.2
      lodash.uniq: 4.5.0

  caniuse-lite@1.0.30001617: {}

  case@1.6.3: {}

  chalk@1.1.3:
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0

  chalk@2.3.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@3.0.0:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  charm@0.1.2: {}

  chokidar@3.5.3:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chrome-trace-event@1.0.3: {}

  ci-info@3.8.0: {}

  circular-dependency-plugin@5.2.2(webpack@5.78.0):
    dependencies:
      webpack: 5.78.0(webpack-cli@5.0.1)

  classnames@2.3.2: {}

  clean-css@5.3.2:
    dependencies:
      source-map: 0.6.1

  cli-cursor@2.1.0:
    dependencies:
      restore-cursor: 2.0.0

  cli-tableau@2.0.1:
    dependencies:
      chalk: 3.0.0

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clone-deep@4.0.1:
    dependencies:
      is-plain-object: 2.0.4
      kind-of: 6.0.3
      shallow-clone: 3.0.1

  co@4.6.0: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  colord@2.9.3: {}

  colorette@2.0.19: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@2.15.1: {}

  commander@2.20.3: {}

  commander@3.0.2: {}

  commander@4.1.1: {}

  commander@7.2.0: {}

  commander@8.3.0: {}

  commander@9.5.0: {}

  common-path-prefix@3.0.0: {}

  commondir@1.0.1: {}

  compressible@2.0.18:
    dependencies:
      mime-db: 1.52.0

  compression@1.7.4:
    dependencies:
      accepts: 1.3.8
      bytes: 3.0.0
      compressible: 2.0.18
      debug: 2.6.9
      on-headers: 1.0.2
      safe-buffer: 5.1.2
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  concat-map@0.0.1: {}

  connect-history-api-fallback@2.0.0: {}

  connect-pause@0.1.1: {}

  consola@2.15.3: {}

  constructs@10.2.69: {}

  content-disposition@0.5.4:
    dependencies:
      safe-buffer: 5.2.1

  content-type@1.0.5: {}

  continuation-local-storage@3.2.1:
    dependencies:
      async-listener: 0.6.10
      emitter-listener: 1.1.2

  convert-source-map@1.9.0: {}

  cookie-signature@1.0.6: {}

  cookie@0.5.0: {}

  cookies@0.8.0:
    dependencies:
      depd: 2.0.0
      keygrip: 1.1.0

  copy-anything@2.0.6:
    dependencies:
      is-what: 3.14.1

  copy-webpack-plugin@11.0.0(webpack@5.78.0):
    dependencies:
      fast-glob: 3.2.12
      glob-parent: 6.0.2
      globby: 13.1.3
      normalize-path: 3.0.0
      schema-utils: 4.0.0
      serialize-javascript: 6.0.1
      webpack: 5.78.0(webpack-cli@5.0.1)

  core-js-compat@3.30.0:
    dependencies:
      browserslist: 4.21.5

  core-js-pure@3.30.0: {}

  core-js@3.30.1: {}

  core-util-is@1.0.3: {}

  cors@2.8.5:
    dependencies:
      object-assign: 4.1.1
      vary: 1.1.2

  cosmiconfig-typescript-loader@4.3.0(@types/node@18.15.11)(cosmiconfig@8.1.3)(ts-node@10.9.2)(typescript@5.0.3):
    dependencies:
      '@types/node': 18.15.11
      cosmiconfig: 8.1.3
      ts-node: 10.9.2(@types/node@18.15.11)(typescript@5.0.3)
      typescript: 5.0.3

  cosmiconfig@7.1.0:
    dependencies:
      '@types/parse-json': 4.0.0
      import-fresh: 3.3.0
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2

  cosmiconfig@8.1.3:
    dependencies:
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      parse-json: 5.2.0
      path-type: 4.0.0

  create-require@1.1.1: {}

  croner@4.1.97: {}

  cross-spawn@7.0.3:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  css-blank-pseudo@3.0.3(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-selector-parser: 6.0.11

  css-color-keywords@1.0.0: {}

  css-declaration-sorter@6.4.0(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21

  css-has-pseudo@3.0.4(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-selector-parser: 6.0.11

  css-loader@6.7.3(webpack@5.78.0):
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.21)
      postcss: 8.4.21
      postcss-modules-extract-imports: 3.0.0(postcss@8.4.21)
      postcss-modules-local-by-default: 4.0.0(postcss@8.4.21)
      postcss-modules-scope: 3.0.0(postcss@8.4.21)
      postcss-modules-values: 4.0.0(postcss@8.4.21)
      postcss-value-parser: 4.2.0
      semver: 7.3.8
      webpack: 5.78.0(webpack-cli@5.0.1)

  css-minimizer-webpack-plugin@4.2.2(webpack@5.78.0):
    dependencies:
      cssnano: 5.1.15(postcss@8.4.21)
      jest-worker: 29.5.0
      postcss: 8.4.21
      schema-utils: 4.0.0
      serialize-javascript: 6.0.1
      source-map: 0.6.1
      webpack: 5.78.0(webpack-cli@5.0.1)

  css-prefers-color-scheme@6.0.3(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38

  css-select@4.3.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1

  css-to-react-native@3.2.0:
    dependencies:
      camelize: 1.0.1
      css-color-keywords: 1.0.0
      postcss-value-parser: 4.2.0

  css-tree@1.1.3:
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1

  css-what@6.1.0: {}

  cssdb@7.5.3: {}

  cssesc@3.0.0: {}

  cssnano-preset-default@5.2.14(postcss@8.4.21):
    dependencies:
      css-declaration-sorter: 6.4.0(postcss@8.4.21)
      cssnano-utils: 3.1.0(postcss@8.4.21)
      postcss: 8.4.21
      postcss-calc: 8.2.4(postcss@8.4.21)
      postcss-colormin: 5.3.1(postcss@8.4.21)
      postcss-convert-values: 5.1.3(postcss@8.4.21)
      postcss-discard-comments: 5.1.2(postcss@8.4.21)
      postcss-discard-duplicates: 5.1.0(postcss@8.4.21)
      postcss-discard-empty: 5.1.1(postcss@8.4.21)
      postcss-discard-overridden: 5.1.0(postcss@8.4.21)
      postcss-merge-longhand: 5.1.7(postcss@8.4.21)
      postcss-merge-rules: 5.1.4(postcss@8.4.21)
      postcss-minify-font-values: 5.1.0(postcss@8.4.21)
      postcss-minify-gradients: 5.1.1(postcss@8.4.21)
      postcss-minify-params: 5.1.4(postcss@8.4.21)
      postcss-minify-selectors: 5.2.1(postcss@8.4.21)
      postcss-normalize-charset: 5.1.0(postcss@8.4.21)
      postcss-normalize-display-values: 5.1.0(postcss@8.4.21)
      postcss-normalize-positions: 5.1.1(postcss@8.4.21)
      postcss-normalize-repeat-style: 5.1.1(postcss@8.4.21)
      postcss-normalize-string: 5.1.0(postcss@8.4.21)
      postcss-normalize-timing-functions: 5.1.0(postcss@8.4.21)
      postcss-normalize-unicode: 5.1.1(postcss@8.4.21)
      postcss-normalize-url: 5.1.0(postcss@8.4.21)
      postcss-normalize-whitespace: 5.1.1(postcss@8.4.21)
      postcss-ordered-values: 5.1.3(postcss@8.4.21)
      postcss-reduce-initial: 5.1.2(postcss@8.4.21)
      postcss-reduce-transforms: 5.1.0(postcss@8.4.21)
      postcss-svgo: 5.1.0(postcss@8.4.21)
      postcss-unique-selectors: 5.1.1(postcss@8.4.21)

  cssnano-utils@3.1.0(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21

  cssnano@5.1.15(postcss@8.4.21):
    dependencies:
      cssnano-preset-default: 5.2.14(postcss@8.4.21)
      lilconfig: 2.1.0
      postcss: 8.4.21
      yaml: 1.10.2

  csso@4.2.0:
    dependencies:
      css-tree: 1.1.3

  csstype@3.1.2: {}

  culvert@0.1.2: {}

  data-uri-to-buffer@3.0.1: {}

  dayjs@1.11.9: {}

  dayjs@1.8.36: {}

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@3.1.0:
    dependencies:
      ms: 2.0.0

  debug@3.2.7:
    dependencies:
      ms: 2.1.3

  debug@4.3.4(supports-color@5.5.0):
    dependencies:
      ms: 2.1.2
      supports-color: 5.5.0

  deep-equal@1.0.1: {}

  deep-is@0.1.4: {}

  deepmerge@4.3.1: {}

  default-gateway@6.0.3:
    dependencies:
      execa: 5.1.1

  define-lazy-prop@2.0.0: {}

  define-properties@1.2.0:
    dependencies:
      has-property-descriptors: 1.0.0
      object-keys: 1.1.1

  degenerator@3.0.4:
    dependencies:
      ast-types: 0.13.4
      escodegen: 1.14.3
      esprima: 4.0.1
      vm2: 3.9.19

  delayed-stream@1.0.0: {}

  delegates@1.0.0: {}

  depd@1.1.2: {}

  depd@2.0.0: {}

  destroy@1.2.0: {}

  detect-node@2.1.0: {}

  didyoumean@1.2.2: {}

  diff@4.0.2: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  dlv@1.1.3: {}

  dns-equal@1.0.0: {}

  dns-packet@5.5.0:
    dependencies:
      '@leichtgewicht/ip-codec': 2.0.4

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  doctrine@3.0.0:
    dependencies:
      esutils: 2.0.3

  dom-align@1.12.4: {}

  dom-converter@0.2.0:
    dependencies:
      utila: 0.4.0

  dom-serializer@1.4.1:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      entities: 2.2.0

  domelementtype@2.3.0: {}

  domhandler@4.3.1:
    dependencies:
      domelementtype: 2.3.0

  domutils@2.8.0:
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.3.0
      domhandler: 4.3.1

  dot-case@3.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.5.0

  duplexer@0.1.2: {}

  ee-first@1.1.1: {}

  electron-to-chromium@1.4.355: {}

  emitter-listener@1.1.2:
    dependencies:
      shimmer: 1.2.1

  emoji-regex@8.0.0: {}

  emojis-list@3.0.0: {}

  encodeurl@1.0.2: {}

  enhanced-resolve@5.12.0:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.1

  enquirer@2.3.6:
    dependencies:
      ansi-colors: 4.1.3

  entities@2.2.0: {}

  entities@4.4.0: {}

  envinfo@7.8.1: {}

  errno@0.1.8:
    dependencies:
      prr: 1.0.1
    optional: true

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  error-stack-parser@2.1.4:
    dependencies:
      stackframe: 1.3.4

  errorhandler@1.5.1:
    dependencies:
      accepts: 1.3.8
      escape-html: 1.0.3

  es-abstract@1.21.2:
    dependencies:
      array-buffer-byte-length: 1.0.0
      available-typed-arrays: 1.0.5
      call-bind: 1.0.2
      es-set-tostringtag: 2.0.1
      es-to-primitive: 1.2.1
      function.prototype.name: 1.1.5
      get-intrinsic: 1.2.0
      get-symbol-description: 1.0.0
      globalthis: 1.0.3
      gopd: 1.0.1
      has: 1.0.3
      has-property-descriptors: 1.0.0
      has-proto: 1.0.1
      has-symbols: 1.0.3
      internal-slot: 1.0.5
      is-array-buffer: 3.0.2
      is-callable: 1.2.7
      is-negative-zero: 2.0.2
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.2
      is-string: 1.0.7
      is-typed-array: 1.1.10
      is-weakref: 1.0.2
      object-inspect: 1.12.3
      object-keys: 1.1.1
      object.assign: 4.1.4
      regexp.prototype.flags: 1.4.3
      safe-regex-test: 1.0.0
      string.prototype.trim: 1.2.7
      string.prototype.trimend: 1.0.6
      string.prototype.trimstart: 1.0.6
      typed-array-length: 1.0.4
      unbox-primitive: 1.0.2
      which-typed-array: 1.1.9

  es-module-lexer@0.9.3: {}

  es-set-tostringtag@2.0.1:
    dependencies:
      get-intrinsic: 1.2.0
      has: 1.0.3
      has-tostringtag: 1.0.0

  es-shim-unscopables@1.0.0:
    dependencies:
      has: 1.0.3

  es-to-primitive@1.2.1:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.0.5
      is-symbol: 1.0.4

  escalade@3.1.1: {}

  escape-html@1.0.3: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@4.0.0: {}

  escodegen@1.14.3:
    dependencies:
      esprima: 4.0.1
      estraverse: 4.3.0
      esutils: 2.0.3
      optionator: 0.8.3
    optionalDependencies:
      source-map: 0.6.1

  eslint-config-prettier@8.8.0(eslint@7.32.0):
    dependencies:
      eslint: 7.32.0

  eslint-config-standard-jsx@11.0.0(eslint-plugin-react@7.32.2)(eslint@8.38.0):
    dependencies:
      eslint: 8.38.0
      eslint-plugin-react: 7.32.2(eslint@8.38.0)

  eslint-config-standard@17.0.0(eslint-plugin-import@2.27.5)(eslint-plugin-n@15.7.0)(eslint-plugin-promise@6.1.1)(eslint@8.38.0):
    dependencies:
      eslint: 8.38.0
      eslint-plugin-import: 2.27.5(eslint@7.32.0)
      eslint-plugin-n: 15.7.0(eslint@7.32.0)
      eslint-plugin-promise: 6.1.1(eslint@7.32.0)

  eslint-import-resolver-node@0.3.7:
    dependencies:
      debug: 3.2.7
      is-core-module: 2.11.0
      resolve: 1.22.2
    transitivePeerDependencies:
      - supports-color

  eslint-module-utils@2.8.0(eslint-import-resolver-node@0.3.7)(eslint@7.32.0):
    dependencies:
      debug: 3.2.7
      eslint: 7.32.0
      eslint-import-resolver-node: 0.3.7
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-es@4.1.0(eslint@7.32.0):
    dependencies:
      eslint: 7.32.0
      eslint-utils: 2.1.0
      regexpp: 3.2.0

  eslint-plugin-import@2.27.5(eslint@7.32.0):
    dependencies:
      array-includes: 3.1.6
      array.prototype.flat: 1.3.1
      array.prototype.flatmap: 1.3.1
      debug: 3.2.7
      doctrine: 2.1.0
      eslint: 7.32.0
      eslint-import-resolver-node: 0.3.7
      eslint-module-utils: 2.8.0(eslint-import-resolver-node@0.3.7)(eslint@7.32.0)
      has: 1.0.3
      is-core-module: 2.11.0
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.values: 1.1.6
      resolve: 1.22.2
      semver: 6.3.0
      tsconfig-paths: 3.14.2
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color

  eslint-plugin-n@15.7.0(eslint@7.32.0):
    dependencies:
      builtins: 5.0.1
      eslint: 7.32.0
      eslint-plugin-es: 4.1.0(eslint@7.32.0)
      eslint-utils: 3.0.0(eslint@7.32.0)
      ignore: 5.2.4
      is-core-module: 2.11.0
      minimatch: 3.1.2
      resolve: 1.22.2
      semver: 7.5.3

  eslint-plugin-prettier@4.2.1(eslint-config-prettier@8.8.0)(eslint@7.32.0)(prettier@3.2.5):
    dependencies:
      eslint: 7.32.0
      eslint-config-prettier: 8.8.0(eslint@7.32.0)
      prettier: 3.2.5
      prettier-linter-helpers: 1.0.0

  eslint-plugin-promise@6.1.1(eslint@7.32.0):
    dependencies:
      eslint: 7.32.0

  eslint-plugin-react@7.32.2(eslint@7.32.0):
    dependencies:
      array-includes: 3.1.6
      array.prototype.flatmap: 1.3.1
      array.prototype.tosorted: 1.1.1
      doctrine: 2.1.0
      eslint: 7.32.0
      estraverse: 5.3.0
      jsx-ast-utils: 3.3.3
      minimatch: 3.1.2
      object.entries: 1.1.6
      object.fromentries: 2.0.6
      object.hasown: 1.1.2
      object.values: 1.1.6
      prop-types: 15.8.1
      resolve: 2.0.0-next.4
      semver: 6.3.0
      string.prototype.matchall: 4.0.8

  eslint-plugin-react@7.32.2(eslint@8.38.0):
    dependencies:
      array-includes: 3.1.6
      array.prototype.flatmap: 1.3.1
      array.prototype.tosorted: 1.1.1
      doctrine: 2.1.0
      eslint: 8.38.0
      estraverse: 5.3.0
      jsx-ast-utils: 3.3.3
      minimatch: 3.1.2
      object.entries: 1.1.6
      object.fromentries: 2.0.6
      object.hasown: 1.1.2
      object.values: 1.1.6
      prop-types: 15.8.1
      resolve: 2.0.0-next.4
      semver: 6.3.0
      string.prototype.matchall: 4.0.8

  eslint-scope@5.1.1:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-scope@7.2.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-utils@2.1.0:
    dependencies:
      eslint-visitor-keys: 1.3.0

  eslint-utils@3.0.0(eslint@7.32.0):
    dependencies:
      eslint: 7.32.0
      eslint-visitor-keys: 2.1.0

  eslint-visitor-keys@1.3.0: {}

  eslint-visitor-keys@2.1.0: {}

  eslint-visitor-keys@3.4.0: {}

  eslint-webpack-plugin@4.0.0(eslint@7.32.0)(webpack@5.78.0):
    dependencies:
      '@types/eslint': 8.37.0
      eslint: 7.32.0
      jest-worker: 29.5.0
      micromatch: 4.0.5
      normalize-path: 3.0.0
      schema-utils: 4.0.0
      webpack: 5.78.0(webpack-cli@5.0.1)

  eslint@7.32.0:
    dependencies:
      '@babel/code-frame': 7.12.11
      '@eslint/eslintrc': 0.4.3
      '@humanwhocodes/config-array': 0.5.0
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.3
      debug: 4.3.4(supports-color@5.5.0)
      doctrine: 3.0.0
      enquirer: 2.3.6
      escape-string-regexp: 4.0.0
      eslint-scope: 5.1.1
      eslint-utils: 2.1.0
      eslint-visitor-keys: 2.1.0
      espree: 7.3.1
      esquery: 1.5.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      functional-red-black-tree: 1.0.1
      glob-parent: 5.1.2
      globals: 13.20.0
      ignore: 4.0.6
      import-fresh: 3.3.0
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      js-yaml: 3.14.1
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.1
      progress: 2.0.3
      regexpp: 3.2.0
      semver: 7.3.8
      strip-ansi: 6.0.1
      strip-json-comments: 3.1.1
      table: 6.8.1
      text-table: 0.2.0
      v8-compile-cache: 2.3.0
    transitivePeerDependencies:
      - supports-color

  eslint@8.38.0:
    dependencies:
      '@eslint-community/eslint-utils': 4.4.0(eslint@8.38.0)
      '@eslint-community/regexpp': 4.5.0
      '@eslint/eslintrc': 2.0.2
      '@eslint/js': 8.38.0
      '@humanwhocodes/config-array': 0.11.8
      '@humanwhocodes/module-importer': 1.0.1
      '@nodelib/fs.walk': 1.2.8
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.3
      debug: 4.3.4(supports-color@5.5.0)
      doctrine: 3.0.0
      escape-string-regexp: 4.0.0
      eslint-scope: 7.2.0
      eslint-visitor-keys: 3.4.0
      espree: 9.5.1
      esquery: 1.5.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      find-up: 5.0.0
      glob-parent: 6.0.2
      globals: 13.20.0
      grapheme-splitter: 1.0.4
      ignore: 5.2.4
      import-fresh: 3.3.0
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      is-path-inside: 3.0.3
      js-sdsl: 4.4.0
      js-yaml: 4.1.0
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.1
      strip-ansi: 6.0.1
      strip-json-comments: 3.1.1
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color

  espree@7.3.1:
    dependencies:
      acorn: 7.4.1
      acorn-jsx: 5.3.2(acorn@7.4.1)
      eslint-visitor-keys: 1.3.0

  espree@9.5.1:
    dependencies:
      acorn: 8.8.2
      acorn-jsx: 5.3.2(acorn@8.8.2)
      eslint-visitor-keys: 3.4.0

  esprima@4.0.1: {}

  esquery@1.5.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  esutils@2.0.3: {}

  etag@1.8.1: {}

  eventemitter2@0.4.14: {}

  eventemitter2@5.0.1: {}

  eventemitter2@6.4.9: {}

  eventemitter3@4.0.7: {}

  events@1.1.1: {}

  events@3.3.0: {}

  execa@5.1.1:
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  express-urlrewrite@1.4.0:
    dependencies:
      debug: 4.3.4(supports-color@5.5.0)
      path-to-regexp: 1.8.0
    transitivePeerDependencies:
      - supports-color

  express@4.18.2:
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.1
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookie: 0.5.0
      cookie-signature: 1.0.6
      debug: 2.6.9
      depd: 2.0.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.2.0
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.1
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.7
      proxy-addr: 2.0.7
      qs: 6.11.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.18.0
      serve-static: 1.15.0
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  fast-deep-equal@3.1.3: {}

  fast-diff@1.2.0: {}

  fast-glob@3.2.12:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.5

  fast-json-patch@3.1.1: {}

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fastest-levenshtein@1.0.16: {}

  fastq@1.15.0:
    dependencies:
      reusify: 1.0.4

  faye-websocket@0.11.4:
    dependencies:
      websocket-driver: 0.7.4

  fclone@1.0.11: {}

  figures@2.0.0:
    dependencies:
      escape-string-regexp: 1.0.5

  file-entry-cache@6.0.1:
    dependencies:
      flat-cache: 3.0.4

  file-loader@6.2.0(webpack@5.78.0):
    dependencies:
      loader-utils: 2.0.4
      schema-utils: 3.1.1
      webpack: 5.78.0(webpack-cli@5.0.1)

  file-uri-to-path@2.0.0: {}

  fill-range@7.0.1:
    dependencies:
      to-regex-range: 5.0.1

  finalhandler@1.2.0:
    dependencies:
      debug: 2.6.9
      encodeurl: 1.0.2
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  find-cache-dir@3.3.2:
    dependencies:
      commondir: 1.0.1
      make-dir: 3.1.0
      pkg-dir: 4.2.0

  find-up@3.0.0:
    dependencies:
      locate-path: 3.0.0

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@3.0.4:
    dependencies:
      flatted: 3.2.7
      rimraf: 3.0.2

  flatted@3.2.7: {}

  follow-redirects@1.15.2(debug@4.3.4):
    dependencies:
      debug: 4.3.4(supports-color@5.5.0)

  for-each@0.3.3:
    dependencies:
      is-callable: 1.2.7

  fork-ts-checker-webpack-plugin@7.3.0(typescript@5.0.3)(webpack@5.78.0):
    dependencies:
      '@babel/code-frame': 7.21.4
      chalk: 4.1.2
      chokidar: 3.5.3
      cosmiconfig: 7.1.0
      deepmerge: 4.3.1
      fs-extra: 10.1.0
      memfs: 3.5.0
      minimatch: 3.1.2
      node-abort-controller: 3.1.1
      schema-utils: 3.1.1
      semver: 7.3.8
      tapable: 2.2.1
      typescript: 5.0.3
      webpack: 5.78.0(webpack-cli@5.0.1)

  form-data@4.0.0:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  forwarded@0.2.0: {}

  fraction.js@4.2.0: {}

  fresh@0.5.2: {}

  friendly-errors-webpack-plugin@1.7.0(webpack@5.78.0):
    dependencies:
      chalk: 1.1.3
      error-stack-parser: 2.1.4
      string-width: 2.1.1
      webpack: 5.78.0(webpack-cli@5.0.1)

  fs-extra@10.1.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.0

  fs-extra@11.1.1:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.0

  fs-extra@8.1.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 4.0.0
      universalify: 0.1.2

  fs-monkey@1.0.3: {}

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  ftp@0.3.10:
    dependencies:
      readable-stream: 1.1.14
      xregexp: 2.0.0

  function-bind@1.1.1: {}

  function.prototype.name@1.1.5:
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2
      functions-have-names: 1.2.3

  functional-red-black-tree@1.0.1: {}

  functions-have-names@1.2.3: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.2.0:
    dependencies:
      function-bind: 1.1.1
      has: 1.0.3
      has-symbols: 1.0.3

  get-stdin@8.0.0: {}

  get-stream@6.0.1: {}

  get-symbol-description@1.0.0:
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.0

  get-uri@3.0.2:
    dependencies:
      '@tootallnate/once': 1.1.2
      data-uri-to-buffer: 3.0.1
      debug: 4.3.4(supports-color@5.5.0)
      file-uri-to-path: 2.0.0
      fs-extra: 8.1.0
      ftp: 0.3.10
    transitivePeerDependencies:
      - supports-color

  git-node-fs@1.0.0(js-git@0.7.8):
    dependencies:
      js-git: 0.7.8

  git-sha1@0.1.2: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob-to-regexp@0.4.1: {}

  glob@7.1.6:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  globals@11.12.0: {}

  globals@13.20.0:
    dependencies:
      type-fest: 0.20.2

  globalthis@1.0.3:
    dependencies:
      define-properties: 1.2.0

  globby@13.1.3:
    dependencies:
      dir-glob: 3.0.1
      fast-glob: 3.2.12
      ignore: 5.2.4
      merge2: 1.4.1
      slash: 4.0.0

  gopd@1.0.1:
    dependencies:
      get-intrinsic: 1.2.0

  graceful-fs@4.2.11: {}

  grapheme-splitter@1.0.4: {}

  growly@1.3.0: {}

  gzip-size@6.0.0:
    dependencies:
      duplexer: 0.1.2

  handle-thing@2.0.1: {}

  has-ansi@2.0.0:
    dependencies:
      ansi-regex: 2.1.1

  has-bigints@1.0.2: {}

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.0:
    dependencies:
      get-intrinsic: 1.2.0

  has-proto@1.0.1: {}

  has-symbols@1.0.3: {}

  has-tostringtag@1.0.0:
    dependencies:
      has-symbols: 1.0.3

  has@1.0.3:
    dependencies:
      function-bind: 1.1.1

  he@1.2.0: {}

  hoist-non-react-statics@3.3.2:
    dependencies:
      react-is: 16.13.1

  hpack.js@2.1.6:
    dependencies:
      inherits: 2.0.4
      obuf: 1.1.2
      readable-stream: 2.3.8
      wbuf: 1.7.3

  html-entities@2.3.3: {}

  html-minifier-terser@6.1.0:
    dependencies:
      camel-case: 4.1.2
      clean-css: 5.3.2
      commander: 8.3.0
      he: 1.2.0
      param-case: 3.0.4
      relateurl: 0.2.7
      terser: 5.16.8

  html-webpack-plugin@5.5.0(webpack@5.78.0):
    dependencies:
      '@types/html-minifier-terser': 6.1.0
      html-minifier-terser: 6.1.0
      lodash: 4.17.21
      pretty-error: 4.0.0
      tapable: 2.2.1
      webpack: 5.78.0(webpack-cli@5.0.1)

  htmlparser2@6.1.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      domutils: 2.8.0
      entities: 2.2.0

  http-assert@1.5.0:
    dependencies:
      deep-equal: 1.0.1
      http-errors: 1.8.1

  http-deceiver@1.2.7: {}

  http-errors@1.6.3:
    dependencies:
      depd: 1.1.2
      inherits: 2.0.3
      setprototypeof: 1.1.0
      statuses: 1.5.0

  http-errors@1.8.1:
    dependencies:
      depd: 1.1.2
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 1.5.0
      toidentifier: 1.0.1

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  http-parser-js@0.5.8: {}

  http-proxy-agent@4.0.1:
    dependencies:
      '@tootallnate/once': 1.1.2
      agent-base: 6.0.2
      debug: 4.3.4(supports-color@5.5.0)
    transitivePeerDependencies:
      - supports-color

  http-proxy-middleware@2.0.6(@types/express@4.17.17):
    dependencies:
      '@types/express': 4.17.17
      '@types/http-proxy': 1.17.10
      http-proxy: 1.18.1
      is-glob: 4.0.3
      is-plain-obj: 3.0.0
      micromatch: 4.0.5
    transitivePeerDependencies:
      - debug

  http-proxy@1.18.1:
    dependencies:
      eventemitter3: 4.0.7
      follow-redirects: 1.15.2(debug@4.3.4)
      requires-port: 1.0.0
    transitivePeerDependencies:
      - debug

  https-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.3.4(supports-color@5.5.0)
    transitivePeerDependencies:
      - supports-color

  human-signals@2.1.0: {}

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2
    optional: true

  icss-utils@5.1.0(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21

  ieee754@1.1.13: {}

  ignore-by-default@1.0.1: {}

  ignore-loader@0.1.2: {}

  ignore@4.0.6: {}

  ignore@5.2.4: {}

  image-size@0.5.5:
    optional: true

  import-fresh@3.3.0:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-local@3.1.0:
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0

  imurmurhash@0.1.4: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.3: {}

  inherits@2.0.4: {}

  ini@1.3.8: {}

  internal-slot@1.0.5:
    dependencies:
      get-intrinsic: 1.2.0
      has: 1.0.3
      side-channel: 1.0.4

  interpret@3.1.1: {}

  intl-messageformat@10.3.4:
    dependencies:
      '@formatjs/ecma402-abstract': 1.14.3
      '@formatjs/fast-memoize': 2.0.1
      '@formatjs/icu-messageformat-parser': 2.3.1
      tslib: 2.5.0

  invariant@2.2.4:
    dependencies:
      loose-envify: 1.4.0

  ip@1.1.8: {}

  ip@2.0.0: {}

  ipaddr.js@1.9.1: {}

  ipaddr.js@2.0.1: {}

  is-arguments@1.1.1:
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0

  is-array-buffer@3.0.2:
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.0
      is-typed-array: 1.1.10

  is-arrayish@0.2.1: {}

  is-bigint@1.0.4:
    dependencies:
      has-bigints: 1.0.2

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.2.0

  is-boolean-object@1.1.2:
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0

  is-callable@1.2.7: {}

  is-core-module@2.11.0:
    dependencies:
      has: 1.0.3

  is-date-object@1.0.5:
    dependencies:
      has-tostringtag: 1.0.0

  is-docker@2.2.1: {}

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@2.0.0: {}

  is-fullwidth-code-point@3.0.0: {}

  is-generator-function@1.0.10:
    dependencies:
      has-tostringtag: 1.0.0

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-negative-zero@2.0.2: {}

  is-number-object@1.0.7:
    dependencies:
      has-tostringtag: 1.0.0

  is-number@7.0.0: {}

  is-path-inside@3.0.3: {}

  is-plain-obj@3.0.0: {}

  is-plain-object@2.0.4:
    dependencies:
      isobject: 3.0.1

  is-promise@2.2.2: {}

  is-regex@1.1.4:
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0

  is-shared-array-buffer@1.0.2:
    dependencies:
      call-bind: 1.0.2

  is-stream@2.0.1: {}

  is-string@1.0.7:
    dependencies:
      has-tostringtag: 1.0.0

  is-symbol@1.0.4:
    dependencies:
      has-symbols: 1.0.3

  is-typed-array@1.1.10:
    dependencies:
      available-typed-arrays: 1.0.5
      call-bind: 1.0.2
      for-each: 0.3.3
      gopd: 1.0.1
      has-tostringtag: 1.0.0

  is-weakref@1.0.2:
    dependencies:
      call-bind: 1.0.2

  is-what@3.14.1: {}

  is-wsl@2.2.0:
    dependencies:
      is-docker: 2.2.1

  isarray@0.0.1: {}

  isarray@1.0.0: {}

  isexe@2.0.0: {}

  isobject@3.0.1: {}

  jest-util@29.5.0:
    dependencies:
      '@jest/types': 29.5.0
      '@types/node': 18.15.11
      chalk: 4.1.2
      ci-info: 3.8.0
      graceful-fs: 4.2.11
      picomatch: 2.3.1

  jest-worker@27.5.1:
    dependencies:
      '@types/node': 18.15.11
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jest-worker@29.5.0:
    dependencies:
      '@types/node': 18.15.11
      jest-util: 29.5.0
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jiti@1.18.2: {}

  jju@1.4.0: {}

  jmespath@0.16.0: {}

  js-git@0.7.8:
    dependencies:
      bodec: 0.1.0
      culvert: 0.1.2
      git-sha1: 0.1.2
      pako: 0.2.9

  js-sdsl@4.4.0: {}

  js-tokens@4.0.0: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsesc@0.5.0: {}

  jsesc@2.5.2: {}

  json-parse-better-errors@1.0.2: {}

  json-parse-even-better-errors@2.3.1: {}

  json-parse-helpfulerror@1.0.3:
    dependencies:
      jju: 1.4.0

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-server@0.17.3:
    dependencies:
      body-parser: 1.20.1
      chalk: 4.1.2
      compression: 1.7.4
      connect-pause: 0.1.1
      cors: 2.8.5
      errorhandler: 1.5.1
      express: 4.18.2
      express-urlrewrite: 1.4.0
      json-parse-helpfulerror: 1.0.3
      lodash: 4.17.21
      lodash-id: 0.14.1
      lowdb: 1.0.0
      method-override: 3.0.0
      morgan: 1.10.0
      nanoid: 3.3.6
      please-upgrade-node: 3.2.0
      pluralize: 8.0.0
      server-destroy: 1.0.1
      standard: 17.0.0
      yargs: 17.7.1
    transitivePeerDependencies:
      - '@typescript-eslint/parser'
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color

  json-stable-stringify-without-jsonify@1.0.1: {}

  json-stringify-safe@5.0.1:
    optional: true

  json5@1.0.2:
    dependencies:
      minimist: 1.2.8

  json5@2.2.3: {}

  jsonfile@4.0.0:
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.0
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonschema@1.4.1: {}

  jsx-ast-utils@3.3.3:
    dependencies:
      array-includes: 3.1.6
      object.assign: 4.1.4

  keygrip@1.1.0:
    dependencies:
      tsscmp: 1.0.6

  kind-of@6.0.3: {}

  klona@2.0.6: {}

  koa-compose@4.1.0: {}

  koa-convert@2.0.0:
    dependencies:
      co: 4.6.0
      koa-compose: 4.1.0

  koa-create-context@1.0.2(@types/koa@2.13.6)(koa@2.14.1):
    dependencies:
      '@types/koa': 2.13.6
      koa: 2.14.1
      mock-req: 0.2.0
      mock-res: 0.5.0

  koa-mount@4.0.0:
    dependencies:
      debug: 4.3.4(supports-color@5.5.0)
      koa-compose: 4.1.0
    transitivePeerDependencies:
      - supports-color

  koa-send@5.0.1:
    dependencies:
      debug: 4.3.4(supports-color@5.5.0)
      http-errors: 1.8.1
      resolve-path: 1.4.0
    transitivePeerDependencies:
      - supports-color

  koa-static@5.0.0:
    dependencies:
      debug: 3.2.7
      koa-send: 5.0.1
    transitivePeerDependencies:
      - supports-color

  koa@2.14.1:
    dependencies:
      accepts: 1.3.8
      cache-content-type: 1.0.1
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookies: 0.8.0
      debug: 4.3.4(supports-color@5.5.0)
      delegates: 1.0.0
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      fresh: 0.5.2
      http-assert: 1.5.0
      http-errors: 1.8.1
      is-generator-function: 1.0.10
      koa-compose: 4.1.0
      koa-convert: 2.0.0
      on-finished: 2.4.1
      only: 0.0.2
      parseurl: 1.3.3
      statuses: 1.5.0
      type-is: 1.6.18
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  launch-editor@2.6.0:
    dependencies:
      picocolors: 1.0.0
      shell-quote: 1.8.0

  lazy@1.0.11: {}

  less-loader@11.1.0(less@4.1.3)(webpack@5.78.0):
    dependencies:
      klona: 2.0.6
      less: 4.1.3
      webpack: 5.78.0(webpack-cli@5.0.1)

  less@4.1.3:
    dependencies:
      copy-anything: 2.0.6
      parse-node-version: 1.0.1
      tslib: 2.5.0
    optionalDependencies:
      errno: 0.1.8
      graceful-fs: 4.2.11
      image-size: 0.5.5
      make-dir: 2.1.0
      mime: 1.6.0
      needle: 3.3.1
      source-map: 0.6.1

  levn@0.3.0:
    dependencies:
      prelude-ls: 1.1.2
      type-check: 0.3.2

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lilconfig@2.1.0: {}

  lines-and-columns@1.2.4: {}

  load-json-file@5.3.0:
    dependencies:
      graceful-fs: 4.2.11
      parse-json: 4.0.0
      pify: 4.0.1
      strip-bom: 3.0.0
      type-fest: 0.3.1

  loader-runner@4.3.0: {}

  loader-utils@2.0.4:
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 2.2.3

  locate-path@3.0.0:
    dependencies:
      p-locate: 3.0.0
      path-exists: 3.0.0

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash-id@0.14.1: {}

  lodash.debounce@4.0.8: {}

  lodash.memoize@4.1.2: {}

  lodash.merge@4.6.2: {}

  lodash.truncate@4.4.2: {}

  lodash.uniq@4.5.0: {}

  lodash@4.17.21: {}

  log-driver@1.2.7: {}

  log-update@2.3.0:
    dependencies:
      ansi-escapes: 3.2.0
      cli-cursor: 2.1.0
      wrap-ansi: 3.0.1

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lowdb@1.0.0:
    dependencies:
      graceful-fs: 4.2.11
      is-promise: 2.2.2
      lodash: 4.17.21
      pify: 3.0.0
      steno: 0.4.4

  lower-case@2.0.2:
    dependencies:
      tslib: 2.5.0

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lru-cache@6.0.0:
    dependencies:
      yallist: 4.0.0

  make-dir@2.1.0:
    dependencies:
      pify: 4.0.1
      semver: 5.7.1
    optional: true

  make-dir@3.1.0:
    dependencies:
      semver: 6.3.0

  make-error@1.3.6: {}

  mdn-data@2.0.14: {}

  media-typer@0.3.0: {}

  memfs@3.5.0:
    dependencies:
      fs-monkey: 1.0.3

  merge-descriptors@1.0.1: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  method-override@3.0.0:
    dependencies:
      debug: 3.1.0
      methods: 1.1.2
      parseurl: 1.3.3
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  methods@1.1.2: {}

  micromatch@4.0.5:
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0: {}

  mimic-fn@1.2.0: {}

  mimic-fn@2.1.0: {}

  mini-css-extract-plugin@2.7.5(webpack@5.78.0):
    dependencies:
      schema-utils: 4.0.0
      webpack: 5.78.0(webpack-cli@5.0.1)

  minimalistic-assert@1.0.1: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimist@1.2.8: {}

  mkdirp@1.0.4: {}

  mock-req@0.2.0: {}

  mock-res@0.5.0: {}

  module-details-from-path@1.0.3: {}

  morgan@1.10.0:
    dependencies:
      basic-auth: 2.0.1
      debug: 2.6.9
      depd: 2.0.0
      on-finished: 2.3.0
      on-headers: 1.0.2
    transitivePeerDependencies:
      - supports-color

  mrmime@1.0.1: {}

  ms@2.0.0: {}

  ms@2.1.2: {}

  ms@2.1.3: {}

  multicast-dns@7.2.5:
    dependencies:
      dns-packet: 5.5.0
      thunky: 1.1.0

  mute-stream@0.0.8: {}

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nanoid@3.3.6: {}

  nanoid@3.3.7: {}

  natural-compare@1.4.0: {}

  needle@2.4.0:
    dependencies:
      debug: 3.2.7
      iconv-lite: 0.4.24
      sax: 1.2.4
    transitivePeerDependencies:
      - supports-color

  needle@3.3.1:
    dependencies:
      iconv-lite: 0.6.3
      sax: 1.2.4
    optional: true

  negotiator@0.6.3: {}

  neo-async@2.6.2: {}

  netmask@2.0.2: {}

  no-case@3.0.4:
    dependencies:
      lower-case: 2.0.2
      tslib: 2.5.0

  node-abort-controller@3.1.1: {}

  node-forge@1.3.1: {}

  node-notifier@9.0.1:
    dependencies:
      growly: 1.3.0
      is-wsl: 2.2.0
      semver: 7.5.3
      shellwords: 0.1.1
      uuid: 8.3.2
      which: 2.0.2

  node-releases@2.0.10: {}

  nodemon@3.1.0:
    dependencies:
      chokidar: 3.5.3
      debug: 4.3.4(supports-color@5.5.0)
      ignore-by-default: 1.0.1
      minimatch: 3.1.2
      pstree.remy: 1.1.8
      semver: 7.5.3
      simple-update-notifier: 2.0.0
      supports-color: 5.5.0
      touch: 3.1.0
      undefsafe: 2.0.5

  nopt@1.0.10:
    dependencies:
      abbrev: 1.1.1

  normalize-path@3.0.0: {}

  normalize-range@0.1.2: {}

  normalize-url@6.1.0: {}

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  nssocket@0.6.0:
    dependencies:
      eventemitter2: 0.4.14
      lazy: 1.0.11

  nth-check@2.1.1:
    dependencies:
      boolbase: 1.0.0

  null-loader@4.0.1(webpack@5.78.0):
    dependencies:
      loader-utils: 2.0.4
      schema-utils: 3.1.1
      webpack: 5.78.0(webpack-cli@5.0.1)

  object-assign@4.1.1: {}

  object-hash@3.0.0: {}

  object-inspect@1.12.3: {}

  object-keys@1.1.1: {}

  object.assign@4.1.4:
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      has-symbols: 1.0.3
      object-keys: 1.1.1

  object.entries@1.1.6:
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2

  object.fromentries@2.0.6:
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2

  object.hasown@1.1.2:
    dependencies:
      define-properties: 1.2.0
      es-abstract: 1.21.2

  object.values@1.1.6:
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2

  obuf@1.1.2: {}

  on-finished@2.3.0:
    dependencies:
      ee-first: 1.1.1

  on-finished@2.4.1:
    dependencies:
      ee-first: 1.1.1

  on-headers@1.0.2: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@2.0.1:
    dependencies:
      mimic-fn: 1.2.0

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  only@0.0.2: {}

  open@8.4.2:
    dependencies:
      define-lazy-prop: 2.0.0
      is-docker: 2.2.1
      is-wsl: 2.2.0

  opener@1.5.2: {}

  optionator@0.8.3:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.3.0
      prelude-ls: 1.1.2
      type-check: 0.3.2
      word-wrap: 1.2.3

  optionator@0.9.1:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.3

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@3.0.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-retry@4.6.2:
    dependencies:
      '@types/retry': 0.12.0
      retry: 0.13.1

  p-try@2.2.0: {}

  pac-proxy-agent@5.0.0:
    dependencies:
      '@tootallnate/once': 1.1.2
      agent-base: 6.0.2
      debug: 4.3.4(supports-color@5.5.0)
      get-uri: 3.0.2
      http-proxy-agent: 4.0.1
      https-proxy-agent: 5.0.1
      pac-resolver: 5.0.1
      raw-body: 2.5.1
      socks-proxy-agent: 5.0.1
    transitivePeerDependencies:
      - supports-color

  pac-resolver@5.0.1:
    dependencies:
      degenerator: 3.0.4
      ip: 1.1.8
      netmask: 2.0.2

  pako@0.2.9: {}

  param-case@3.0.4:
    dependencies:
      dot-case: 3.0.4
      tslib: 2.5.0

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-json@4.0.0:
    dependencies:
      error-ex: 1.3.2
      json-parse-better-errors: 1.0.2

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.21.4
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse-node-version@1.0.1: {}

  parseurl@1.3.3: {}

  pascal-case@3.1.2:
    dependencies:
      no-case: 3.0.4
      tslib: 2.5.0

  path-exists@3.0.0: {}

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-to-regexp@0.1.7: {}

  path-to-regexp@1.8.0:
    dependencies:
      isarray: 0.0.1

  path-to-regexp@6.2.1: {}

  path-type@4.0.0: {}

  picocolors@1.0.0: {}

  picomatch@2.3.1: {}

  pidusage@2.0.21:
    dependencies:
      safe-buffer: 5.2.1
    optional: true

  pidusage@3.0.2:
    dependencies:
      safe-buffer: 5.2.1

  pify@2.3.0: {}

  pify@3.0.0: {}

  pify@4.0.1: {}

  pirates@4.0.5: {}

  pkg-conf@3.1.0:
    dependencies:
      find-up: 3.0.0
      load-json-file: 5.3.0

  pkg-dir@4.2.0:
    dependencies:
      find-up: 4.1.0

  please-upgrade-node@3.2.0:
    dependencies:
      semver-compare: 1.0.0

  pluralize@8.0.0: {}

  pm2-axon-rpc@0.7.1:
    dependencies:
      debug: 4.3.4(supports-color@5.5.0)
    transitivePeerDependencies:
      - supports-color

  pm2-axon@4.0.1:
    dependencies:
      amp: 0.3.1
      amp-message: 0.1.2
      debug: 4.3.4(supports-color@5.5.0)
      escape-string-regexp: 4.0.0
    transitivePeerDependencies:
      - supports-color

  pm2-deploy@1.0.2:
    dependencies:
      run-series: 1.1.9
      tv4: 1.3.0

  pm2-multimeter@0.1.2:
    dependencies:
      charm: 0.1.2

  pm2-sysmonit@1.2.8:
    dependencies:
      async: 3.2.4
      debug: 4.3.4(supports-color@5.5.0)
      pidusage: 2.0.21
      systeminformation: 5.18.6
      tx2: 1.0.5
    transitivePeerDependencies:
      - supports-color
    optional: true

  pm2@5.3.0:
    dependencies:
      '@pm2/agent': 2.0.1
      '@pm2/io': 5.0.0
      '@pm2/js-api': 0.6.7
      '@pm2/pm2-version-check': 1.0.4
      async: 3.2.4
      blessed: 0.1.81
      chalk: 3.0.0
      chokidar: 3.5.3
      cli-tableau: 2.0.1
      commander: 2.15.1
      croner: 4.1.97
      dayjs: 1.11.9
      debug: 4.3.4(supports-color@5.5.0)
      enquirer: 2.3.6
      eventemitter2: 5.0.1
      fclone: 1.0.11
      mkdirp: 1.0.4
      needle: 2.4.0
      pidusage: 3.0.2
      pm2-axon: 4.0.1
      pm2-axon-rpc: 0.7.1
      pm2-deploy: 1.0.2
      pm2-multimeter: 0.1.2
      promptly: 2.2.0
      semver: 7.5.3
      source-map-support: 0.5.21
      sprintf-js: 1.1.2
      vizion: 2.2.1
      yamljs: 0.3.0
    optionalDependencies:
      pm2-sysmonit: 1.2.8
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  postcss-attribute-case-insensitive@5.0.2(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-selector-parser: 6.0.11

  postcss-calc@8.2.4(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21
      postcss-selector-parser: 6.0.11
      postcss-value-parser: 4.2.0

  postcss-clamp@4.1.0(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  postcss-color-functional-notation@4.2.4(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  postcss-color-hex-alpha@8.0.4(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  postcss-color-rebeccapurple@7.1.1(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  postcss-colormin@5.3.1(postcss@8.4.21):
    dependencies:
      browserslist: 4.21.5
      caniuse-api: 3.0.0
      colord: 2.9.3
      postcss: 8.4.21
      postcss-value-parser: 4.2.0

  postcss-convert-values@5.1.3(postcss@8.4.21):
    dependencies:
      browserslist: 4.21.5
      postcss: 8.4.21
      postcss-value-parser: 4.2.0

  postcss-custom-media@8.0.2(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  postcss-custom-properties@12.1.11(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  postcss-custom-selectors@6.0.3(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-selector-parser: 6.0.11

  postcss-dir-pseudo-class@6.0.5(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-selector-parser: 6.0.11

  postcss-discard-comments@5.1.2(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21

  postcss-discard-duplicates@5.1.0(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21

  postcss-discard-empty@5.1.1(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21

  postcss-discard-overridden@5.1.0(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21

  postcss-double-position-gradients@3.1.2(postcss@8.4.38):
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0(postcss@8.4.38)
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  postcss-env-function@4.0.6(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  postcss-focus-visible@6.0.4(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-selector-parser: 6.0.11

  postcss-focus-within@5.0.4(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-selector-parser: 6.0.11

  postcss-font-variant@5.0.0(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38

  postcss-gap-properties@3.0.5(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38

  postcss-image-set-function@4.0.7(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  postcss-import@14.1.0(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.2

  postcss-initial@4.0.1(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38

  postcss-js@4.0.1(postcss@8.4.38):
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.4.38

  postcss-lab-function@4.2.1(postcss@8.4.38):
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0(postcss@8.4.38)
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  postcss-load-config@3.1.4(postcss@8.4.38)(ts-node@10.9.2):
    dependencies:
      lilconfig: 2.1.0
      postcss: 8.4.38
      ts-node: 10.9.2(@types/node@18.15.11)(typescript@5.0.3)
      yaml: 1.10.2

  postcss-loader@7.2.4(@types/node@18.15.11)(postcss@8.4.38)(ts-node@10.9.2)(typescript@5.0.3)(webpack@5.78.0):
    dependencies:
      cosmiconfig: 8.1.3
      cosmiconfig-typescript-loader: 4.3.0(@types/node@18.15.11)(cosmiconfig@8.1.3)(ts-node@10.9.2)(typescript@5.0.3)
      klona: 2.0.6
      postcss: 8.4.38
      semver: 7.3.8
      ts-node: 10.9.2(@types/node@18.15.11)(typescript@5.0.3)
      typescript: 5.0.3
      webpack: 5.78.0(webpack-cli@5.0.1)
    transitivePeerDependencies:
      - '@types/node'

  postcss-logical@5.0.4(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38

  postcss-media-minmax@5.0.0(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38

  postcss-merge-longhand@5.1.7(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21
      postcss-value-parser: 4.2.0
      stylehacks: 5.1.1(postcss@8.4.21)

  postcss-merge-rules@5.1.4(postcss@8.4.21):
    dependencies:
      browserslist: 4.21.5
      caniuse-api: 3.0.0
      cssnano-utils: 3.1.0(postcss@8.4.21)
      postcss: 8.4.21
      postcss-selector-parser: 6.0.11

  postcss-minify-font-values@5.1.0(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21
      postcss-value-parser: 4.2.0

  postcss-minify-gradients@5.1.1(postcss@8.4.21):
    dependencies:
      colord: 2.9.3
      cssnano-utils: 3.1.0(postcss@8.4.21)
      postcss: 8.4.21
      postcss-value-parser: 4.2.0

  postcss-minify-params@5.1.4(postcss@8.4.21):
    dependencies:
      browserslist: 4.21.5
      cssnano-utils: 3.1.0(postcss@8.4.21)
      postcss: 8.4.21
      postcss-value-parser: 4.2.0

  postcss-minify-selectors@5.2.1(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21
      postcss-selector-parser: 6.0.11

  postcss-modules-extract-imports@3.0.0(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21

  postcss-modules-local-by-default@4.0.0(postcss@8.4.21):
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.21)
      postcss: 8.4.21
      postcss-selector-parser: 6.0.11
      postcss-value-parser: 4.2.0

  postcss-modules-scope@3.0.0(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21
      postcss-selector-parser: 6.0.11

  postcss-modules-values@4.0.0(postcss@8.4.21):
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.21)
      postcss: 8.4.21

  postcss-nested@6.0.0(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-selector-parser: 6.0.11

  postcss-nesting@10.2.0(postcss@8.4.38):
    dependencies:
      '@csstools/selector-specificity': 2.2.0(postcss-selector-parser@6.0.11)
      postcss: 8.4.38
      postcss-selector-parser: 6.0.11

  postcss-normalize-charset@5.1.0(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21

  postcss-normalize-display-values@5.1.0(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21
      postcss-value-parser: 4.2.0

  postcss-normalize-positions@5.1.1(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21
      postcss-value-parser: 4.2.0

  postcss-normalize-repeat-style@5.1.1(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21
      postcss-value-parser: 4.2.0

  postcss-normalize-string@5.1.0(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21
      postcss-value-parser: 4.2.0

  postcss-normalize-timing-functions@5.1.0(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21
      postcss-value-parser: 4.2.0

  postcss-normalize-unicode@5.1.1(postcss@8.4.21):
    dependencies:
      browserslist: 4.21.5
      postcss: 8.4.21
      postcss-value-parser: 4.2.0

  postcss-normalize-url@5.1.0(postcss@8.4.21):
    dependencies:
      normalize-url: 6.1.0
      postcss: 8.4.21
      postcss-value-parser: 4.2.0

  postcss-normalize-whitespace@5.1.1(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21
      postcss-value-parser: 4.2.0

  postcss-opacity-percentage@1.1.3(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38

  postcss-ordered-values@5.1.3(postcss@8.4.21):
    dependencies:
      cssnano-utils: 3.1.0(postcss@8.4.21)
      postcss: 8.4.21
      postcss-value-parser: 4.2.0

  postcss-overflow-shorthand@3.0.4(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  postcss-page-break@3.0.4(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38

  postcss-place@7.0.5(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  postcss-preset-env@7.8.3(postcss@8.4.38):
    dependencies:
      '@csstools/postcss-cascade-layers': 1.1.1(postcss@8.4.38)
      '@csstools/postcss-color-function': 1.1.1(postcss@8.4.38)
      '@csstools/postcss-font-format-keywords': 1.0.1(postcss@8.4.38)
      '@csstools/postcss-hwb-function': 1.0.2(postcss@8.4.38)
      '@csstools/postcss-ic-unit': 1.0.1(postcss@8.4.38)
      '@csstools/postcss-is-pseudo-class': 2.0.7(postcss@8.4.38)
      '@csstools/postcss-nested-calc': 1.0.0(postcss@8.4.38)
      '@csstools/postcss-normalize-display-values': 1.0.1(postcss@8.4.38)
      '@csstools/postcss-oklab-function': 1.1.1(postcss@8.4.38)
      '@csstools/postcss-progressive-custom-properties': 1.3.0(postcss@8.4.38)
      '@csstools/postcss-stepped-value-functions': 1.0.1(postcss@8.4.38)
      '@csstools/postcss-text-decoration-shorthand': 1.0.0(postcss@8.4.38)
      '@csstools/postcss-trigonometric-functions': 1.0.2(postcss@8.4.38)
      '@csstools/postcss-unset-value': 1.0.2(postcss@8.4.38)
      autoprefixer: 10.4.14(postcss@8.4.38)
      browserslist: 4.21.5
      css-blank-pseudo: 3.0.3(postcss@8.4.38)
      css-has-pseudo: 3.0.4(postcss@8.4.38)
      css-prefers-color-scheme: 6.0.3(postcss@8.4.38)
      cssdb: 7.5.3
      postcss: 8.4.38
      postcss-attribute-case-insensitive: 5.0.2(postcss@8.4.38)
      postcss-clamp: 4.1.0(postcss@8.4.38)
      postcss-color-functional-notation: 4.2.4(postcss@8.4.38)
      postcss-color-hex-alpha: 8.0.4(postcss@8.4.38)
      postcss-color-rebeccapurple: 7.1.1(postcss@8.4.38)
      postcss-custom-media: 8.0.2(postcss@8.4.38)
      postcss-custom-properties: 12.1.11(postcss@8.4.38)
      postcss-custom-selectors: 6.0.3(postcss@8.4.38)
      postcss-dir-pseudo-class: 6.0.5(postcss@8.4.38)
      postcss-double-position-gradients: 3.1.2(postcss@8.4.38)
      postcss-env-function: 4.0.6(postcss@8.4.38)
      postcss-focus-visible: 6.0.4(postcss@8.4.38)
      postcss-focus-within: 5.0.4(postcss@8.4.38)
      postcss-font-variant: 5.0.0(postcss@8.4.38)
      postcss-gap-properties: 3.0.5(postcss@8.4.38)
      postcss-image-set-function: 4.0.7(postcss@8.4.38)
      postcss-initial: 4.0.1(postcss@8.4.38)
      postcss-lab-function: 4.2.1(postcss@8.4.38)
      postcss-logical: 5.0.4(postcss@8.4.38)
      postcss-media-minmax: 5.0.0(postcss@8.4.38)
      postcss-nesting: 10.2.0(postcss@8.4.38)
      postcss-opacity-percentage: 1.1.3(postcss@8.4.38)
      postcss-overflow-shorthand: 3.0.4(postcss@8.4.38)
      postcss-page-break: 3.0.4(postcss@8.4.38)
      postcss-place: 7.0.5(postcss@8.4.38)
      postcss-pseudo-class-any-link: 7.1.6(postcss@8.4.38)
      postcss-replace-overflow-wrap: 4.0.0(postcss@8.4.38)
      postcss-selector-not: 6.0.1(postcss@8.4.38)
      postcss-value-parser: 4.2.0

  postcss-pseudo-class-any-link@7.1.6(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-selector-parser: 6.0.11

  postcss-reduce-initial@5.1.2(postcss@8.4.21):
    dependencies:
      browserslist: 4.21.5
      caniuse-api: 3.0.0
      postcss: 8.4.21

  postcss-reduce-transforms@5.1.0(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21
      postcss-value-parser: 4.2.0

  postcss-replace-overflow-wrap@4.0.0(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38

  postcss-selector-not@6.0.1(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-selector-parser: 6.0.11

  postcss-selector-parser@6.0.11:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-svgo@5.1.0(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21
      postcss-value-parser: 4.2.0
      svgo: 2.8.0

  postcss-unique-selectors@5.1.1(postcss@8.4.21):
    dependencies:
      postcss: 8.4.21
      postcss-selector-parser: 6.0.11

  postcss-value-parser@4.2.0: {}

  postcss@8.4.21:
    dependencies:
      nanoid: 3.3.6
      picocolors: 1.0.0
      source-map-js: 1.0.2

  postcss@8.4.38:
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.0.0
      source-map-js: 1.2.0

  prelude-ls@1.1.2: {}

  prelude-ls@1.2.1: {}

  prettier-linter-helpers@1.0.0:
    dependencies:
      fast-diff: 1.2.0

  prettier@3.2.5: {}

  pretty-error@4.0.0:
    dependencies:
      lodash: 4.17.21
      renderkid: 3.0.0

  pretty-time@1.1.0: {}

  process-nextick-args@2.0.1: {}

  process@0.11.10: {}

  progress@2.0.3: {}

  promptly@2.2.0:
    dependencies:
      read: 1.0.7

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  proxy-addr@2.0.7:
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  proxy-agent@5.0.0:
    dependencies:
      agent-base: 6.0.2
      debug: 4.3.4(supports-color@5.5.0)
      http-proxy-agent: 4.0.1
      https-proxy-agent: 5.0.1
      lru-cache: 5.1.1
      pac-proxy-agent: 5.0.0
      proxy-from-env: 1.1.0
      socks-proxy-agent: 5.0.1
    transitivePeerDependencies:
      - supports-color

  proxy-from-env@1.1.0: {}

  prr@1.0.1:
    optional: true

  pstree.remy@1.1.8: {}

  punycode@1.3.2: {}

  punycode@2.3.0: {}

  qs@6.11.0:
    dependencies:
      side-channel: 1.0.4

  qs@6.11.1:
    dependencies:
      side-channel: 1.0.4

  querystring@0.2.0: {}

  queue-microtask@1.2.3: {}

  quick-lru@5.1.1: {}

  randombytes@2.1.0:
    dependencies:
      safe-buffer: 5.2.1

  range-parser@1.2.1: {}

  raw-body@2.5.1:
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  rc-align@4.0.15(react-dom@18.3.1)(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.21.0
      classnames: 2.3.2
      dom-align: 1.12.4
      rc-util: 5.30.0(react-dom@18.3.1)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      resize-observer-polyfill: 1.5.1

  rc-dropdown@4.0.1(react-dom@18.3.1)(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.21.0
      classnames: 2.3.2
      rc-trigger: 5.3.4(react-dom@18.3.1)(react@18.3.1)
      rc-util: 5.30.0(react-dom@18.3.1)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-motion@2.7.3(react-dom@18.3.1)(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.21.0
      classnames: 2.3.2
      rc-util: 5.30.0(react-dom@18.3.1)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-trigger@5.3.4(react-dom@18.3.1)(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.21.0
      classnames: 2.3.2
      rc-align: 4.0.15(react-dom@18.3.1)(react@18.3.1)
      rc-motion: 2.7.3(react-dom@18.3.1)(react@18.3.1)
      rc-util: 5.30.0(react-dom@18.3.1)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-util@5.30.0(react-dom@18.3.1)(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.21.0
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-is: 16.13.1

  react-dom@18.3.1(react@18.3.1):
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.23.2

  react-fast-compare@3.2.1: {}

  react-helmet-async@1.3.0(react-dom@18.3.1)(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.21.0
      invariant: 2.2.4
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-fast-compare: 3.2.1
      shallowequal: 1.1.0

  react-intl@6.4.0(react@18.3.1)(typescript@5.0.3):
    dependencies:
      '@formatjs/ecma402-abstract': 1.14.3
      '@formatjs/icu-messageformat-parser': 2.3.1
      '@formatjs/intl': 2.7.0(typescript@5.0.3)
      '@formatjs/intl-displaynames': 6.3.0
      '@formatjs/intl-listformat': 7.2.0
      '@types/hoist-non-react-statics': 3.3.1
      '@types/react': 18.3.2
      hoist-non-react-statics: 3.3.2
      intl-messageformat: 10.3.4
      react: 18.3.1
      tslib: 2.5.0
      typescript: 5.0.3

  react-is@16.13.1: {}

  react-is@18.2.0: {}

  react-refresh@0.14.0: {}

  react-router-dom@6.10.0(react-dom@18.3.1)(react@18.3.1):
    dependencies:
      '@remix-run/router': 1.5.0
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-router: 6.10.0(react@18.3.1)

  react-router@6.10.0(react@18.3.1):
    dependencies:
      '@remix-run/router': 1.5.0
      react: 18.3.1

  react@18.3.1:
    dependencies:
      loose-envify: 1.4.0

  read-cache@1.0.0:
    dependencies:
      pify: 2.3.0

  read@1.0.7:
    dependencies:
      mute-stream: 0.0.8

  readable-stream@1.1.14:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 0.0.1
      string_decoder: 0.10.31

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  rechoir@0.8.0:
    dependencies:
      resolve: 1.22.2

  regenerate-unicode-properties@10.1.0:
    dependencies:
      regenerate: 1.4.2

  regenerate@1.4.2: {}

  regenerator-runtime@0.13.11: {}

  regenerator-transform@0.15.1:
    dependencies:
      '@babel/runtime': 7.21.0

  regexp.prototype.flags@1.4.3:
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      functions-have-names: 1.2.3

  regexpp@3.2.0: {}

  regexpu-core@5.3.2:
    dependencies:
      '@babel/regjsgen': 0.8.0
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.1.0
      regjsparser: 0.9.1
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.1.0

  regjsparser@0.9.1:
    dependencies:
      jsesc: 0.5.0

  relateurl@0.2.7: {}

  renderkid@3.0.0:
    dependencies:
      css-select: 4.3.0
      dom-converter: 0.2.0
      htmlparser2: 6.1.0
      lodash: 4.17.21
      strip-ansi: 6.0.1

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  require-in-the-middle@5.2.0:
    dependencies:
      debug: 4.3.4(supports-color@5.5.0)
      module-details-from-path: 1.0.3
      resolve: 1.22.2
    transitivePeerDependencies:
      - supports-color

  requires-port@1.0.0: {}

  resize-observer-polyfill@1.5.1: {}

  resolve-cwd@3.0.0:
    dependencies:
      resolve-from: 5.0.0

  resolve-from@4.0.0: {}

  resolve-from@5.0.0: {}

  resolve-path@1.4.0:
    dependencies:
      http-errors: 1.6.3
      path-is-absolute: 1.0.1

  resolve@1.22.2:
    dependencies:
      is-core-module: 2.11.0
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@2.0.0-next.4:
    dependencies:
      is-core-module: 2.11.0
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@2.0.0:
    dependencies:
      onetime: 2.0.1
      signal-exit: 3.0.7

  retry@0.13.1: {}

  reusify@1.0.4: {}

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  run-series@1.1.9: {}

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  safe-regex-test@1.0.0:
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.0
      is-regex: 1.1.4

  safer-buffer@2.1.2: {}

  sax@1.2.1: {}

  sax@1.2.4: {}

  scheduler@0.23.2:
    dependencies:
      loose-envify: 1.4.0

  schema-utils@2.7.1:
    dependencies:
      '@types/json-schema': 7.0.11
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)

  schema-utils@3.1.1:
    dependencies:
      '@types/json-schema': 7.0.11
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)

  schema-utils@4.0.0:
    dependencies:
      '@types/json-schema': 7.0.11
      ajv: 8.12.0
      ajv-formats: 2.1.1(ajv@8.12.0)
      ajv-keywords: 5.1.0(ajv@8.12.0)

  select-hose@2.0.0: {}

  selfsigned@2.1.1:
    dependencies:
      node-forge: 1.3.1

  semver-compare@1.0.0: {}

  semver@5.7.1: {}

  semver@6.3.0: {}

  semver@7.2.3: {}

  semver@7.3.8:
    dependencies:
      lru-cache: 6.0.0

  semver@7.5.3:
    dependencies:
      lru-cache: 6.0.0

  send@0.18.0:
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  serialize-javascript@6.0.1:
    dependencies:
      randombytes: 2.1.0

  serve-index@1.9.1:
    dependencies:
      accepts: 1.3.8
      batch: 0.6.1
      debug: 2.6.9
      escape-html: 1.0.3
      http-errors: 1.6.3
      mime-types: 2.1.35
      parseurl: 1.3.3
    transitivePeerDependencies:
      - supports-color

  serve-static@1.15.0:
    dependencies:
      encodeurl: 1.0.2
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.18.0
    transitivePeerDependencies:
      - supports-color

  server-destroy@1.0.1: {}

  serverless-http@3.2.0: {}

  setprototypeof@1.1.0: {}

  setprototypeof@1.2.0: {}

  shallow-clone@3.0.1:
    dependencies:
      kind-of: 6.0.3

  shallowequal@1.1.0: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shell-quote@1.8.0: {}

  shellwords@0.1.1: {}

  shimmer@1.2.1: {}

  side-channel@1.0.4:
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.0
      object-inspect: 1.12.3

  signal-exit@3.0.7: {}

  simple-progress-webpack-plugin@1.1.2(webpack@5.78.0):
    dependencies:
      chalk: 2.3.2
      figures: 2.0.0
      log-update: 2.3.0
      webpack: 5.78.0(webpack-cli@5.0.1)

  simple-update-notifier@2.0.0:
    dependencies:
      semver: 7.5.3

  sirv@1.0.19:
    dependencies:
      '@polka/url': 1.0.0-next.21
      mrmime: 1.0.1
      totalist: 1.1.0

  slash@4.0.0: {}

  slice-ansi@4.0.0:
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0

  smart-buffer@4.2.0: {}

  sockjs@0.3.24:
    dependencies:
      faye-websocket: 0.11.4
      uuid: 8.3.2
      websocket-driver: 0.7.4

  socks-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.3.4(supports-color@5.5.0)
      socks: 2.7.1
    transitivePeerDependencies:
      - supports-color

  socks@2.7.1:
    dependencies:
      ip: 2.0.0
      smart-buffer: 4.2.0

  source-list-map@2.0.1: {}

  source-map-js@1.0.2: {}

  source-map-js@1.2.0: {}

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  source-map@0.7.4: {}

  spdy-transport@3.0.0:
    dependencies:
      debug: 4.3.4(supports-color@5.5.0)
      detect-node: 2.1.0
      hpack.js: 2.1.6
      obuf: 1.1.2
      readable-stream: 3.6.2
      wbuf: 1.7.3
    transitivePeerDependencies:
      - supports-color

  spdy@4.0.2:
    dependencies:
      debug: 4.3.4(supports-color@5.5.0)
      handle-thing: 2.0.1
      http-deceiver: 1.2.7
      select-hose: 2.0.0
      spdy-transport: 3.0.0
    transitivePeerDependencies:
      - supports-color

  sprintf-js@1.0.3: {}

  sprintf-js@1.1.2: {}

  stable@0.1.8: {}

  stackframe@1.3.4: {}

  standard-engine@15.0.0:
    dependencies:
      get-stdin: 8.0.0
      minimist: 1.2.8
      pkg-conf: 3.1.0
      xdg-basedir: 4.0.0

  standard@17.0.0:
    dependencies:
      eslint: 8.38.0
      eslint-config-standard: 17.0.0(eslint-plugin-import@2.27.5)(eslint-plugin-n@15.7.0)(eslint-plugin-promise@6.1.1)(eslint@8.38.0)
      eslint-config-standard-jsx: 11.0.0(eslint-plugin-react@7.32.2)(eslint@8.38.0)
      eslint-plugin-import: 2.27.5(eslint@7.32.0)
      eslint-plugin-n: 15.7.0(eslint@7.32.0)
      eslint-plugin-promise: 6.1.1(eslint@7.32.0)
      eslint-plugin-react: 7.32.2(eslint@8.38.0)
      standard-engine: 15.0.0
    transitivePeerDependencies:
      - '@typescript-eslint/parser'
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color

  statuses@1.5.0: {}

  statuses@2.0.1: {}

  std-env@3.3.2: {}

  steno@0.4.4:
    dependencies:
      graceful-fs: 4.2.11

  string-width@2.1.1:
    dependencies:
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 4.0.0

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string.prototype.matchall@4.0.8:
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2
      get-intrinsic: 1.2.0
      has-symbols: 1.0.3
      internal-slot: 1.0.5
      regexp.prototype.flags: 1.4.3
      side-channel: 1.0.4

  string.prototype.trim@1.2.7:
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2

  string.prototype.trimend@1.0.6:
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2

  string.prototype.trimstart@1.0.6:
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.21.2

  string_decoder@0.10.31: {}

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  strip-ansi@3.0.1:
    dependencies:
      ansi-regex: 2.1.1

  strip-ansi@4.0.0:
    dependencies:
      ansi-regex: 3.0.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-bom@3.0.0: {}

  strip-final-newline@2.0.0: {}

  strip-json-comments@3.1.1: {}

  style-resources-loader@1.5.0(webpack@5.78.0):
    dependencies:
      glob: 7.2.3
      loader-utils: 2.0.4
      schema-utils: 2.7.1
      tslib: 2.5.0
      webpack: 5.78.0(webpack-cli@5.0.1)

  styled-components@5.3.9(react-dom@18.3.1)(react-is@18.2.0)(react@18.3.1):
    dependencies:
      '@babel/helper-module-imports': 7.21.4
      '@babel/traverse': 7.21.4(supports-color@5.5.0)
      '@emotion/is-prop-valid': 1.2.0
      '@emotion/stylis': 0.8.5
      '@emotion/unitless': 0.7.5
      babel-plugin-styled-components: 2.1.1(styled-components@5.3.9)
      css-to-react-native: 3.2.0
      hoist-non-react-statics: 3.3.2
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-is: 18.2.0
      shallowequal: 1.1.0
      supports-color: 5.5.0

  stylehacks@5.1.1(postcss@8.4.21):
    dependencies:
      browserslist: 4.21.5
      postcss: 8.4.21
      postcss-selector-parser: 6.0.11

  sucrase@3.32.0:
    dependencies:
      '@jridgewell/gen-mapping': 0.3.3
      commander: 4.1.1
      glob: 7.1.6
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.5
      ts-interface-checker: 0.1.13

  supports-color@2.0.0: {}

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  svg-parser@2.0.4: {}

  svgo@2.8.0:
    dependencies:
      '@trysound/sax': 0.2.0
      commander: 7.2.0
      css-select: 4.3.0
      css-tree: 1.1.3
      csso: 4.2.0
      picocolors: 1.0.0
      stable: 0.1.8

  systeminformation@5.18.6:
    optional: true

  table@6.8.1:
    dependencies:
      ajv: 8.12.0
      lodash.truncate: 4.4.2
      slice-ansi: 4.0.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  tailwindcss@3.3.1(postcss@8.4.38)(ts-node@10.9.2):
    dependencies:
      arg: 5.0.2
      chokidar: 3.5.3
      color-name: 1.1.4
      didyoumean: 1.2.2
      dlv: 1.1.3
      fast-glob: 3.2.12
      glob-parent: 6.0.2
      is-glob: 4.0.3
      jiti: 1.18.2
      lilconfig: 2.1.0
      micromatch: 4.0.5
      normalize-path: 3.0.0
      object-hash: 3.0.0
      picocolors: 1.0.0
      postcss: 8.4.38
      postcss-import: 14.1.0(postcss@8.4.38)
      postcss-js: 4.0.1(postcss@8.4.38)
      postcss-load-config: 3.1.4(postcss@8.4.38)(ts-node@10.9.2)
      postcss-nested: 6.0.0(postcss@8.4.38)
      postcss-selector-parser: 6.0.11
      postcss-value-parser: 4.2.0
      quick-lru: 5.1.1
      resolve: 1.22.2
      sucrase: 3.32.0
    transitivePeerDependencies:
      - ts-node

  tapable@2.2.1: {}

  terser-webpack-plugin@5.3.7(webpack@5.78.0):
    dependencies:
      '@jridgewell/trace-mapping': 0.3.18
      jest-worker: 27.5.1
      schema-utils: 3.1.1
      serialize-javascript: 6.0.1
      terser: 5.16.8
      webpack: 5.78.0(webpack-cli@5.0.1)

  terser@5.16.8:
    dependencies:
      '@jridgewell/source-map': 0.3.3
      acorn: 8.8.2
      commander: 2.20.3
      source-map-support: 0.5.21

  text-table@0.2.0: {}

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  thread-loader@3.0.4(webpack@5.78.0):
    dependencies:
      json-parse-better-errors: 1.0.2
      loader-runner: 4.3.0
      loader-utils: 2.0.4
      neo-async: 2.6.2
      schema-utils: 3.1.1
      webpack: 5.78.0(webpack-cli@5.0.1)

  thunky@1.1.0: {}

  to-fast-properties@2.0.0: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toidentifier@1.0.1: {}

  totalist@1.1.0: {}

  touch@3.1.0:
    dependencies:
      nopt: 1.0.10

  ts-interface-checker@0.1.13: {}

  ts-node@10.9.2(@types/node@18.15.11)(typescript@5.0.3):
    dependencies:
      '@cspotcode/source-map-support': 0.8.1
      '@tsconfig/node10': 1.0.11
      '@tsconfig/node12': 1.0.11
      '@tsconfig/node14': 1.0.3
      '@tsconfig/node16': 1.0.4
      '@types/node': 18.15.11
      acorn: 8.11.3
      acorn-walk: 8.3.2
      arg: 4.1.3
      create-require: 1.1.1
      diff: 4.0.2
      make-error: 1.3.6
      typescript: 5.0.3
      v8-compile-cache-lib: 3.0.1
      yn: 3.1.1

  tsconfig-paths-webpack-plugin@4.0.1:
    dependencies:
      chalk: 4.1.2
      enhanced-resolve: 5.12.0
      tsconfig-paths: 4.2.0

  tsconfig-paths@3.14.2:
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0

  tsconfig-paths@4.2.0:
    dependencies:
      json5: 2.2.3
      minimist: 1.2.8
      strip-bom: 3.0.0

  tslib@1.9.3: {}

  tslib@2.5.0: {}

  tsscmp@1.0.6: {}

  tv4@1.3.0: {}

  tx2@1.0.5:
    dependencies:
      json-stringify-safe: 5.0.1
    optional: true

  type-check@0.3.2:
    dependencies:
      prelude-ls: 1.1.2

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-fest@0.20.2: {}

  type-fest@0.3.1: {}

  type-is@1.6.18:
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35

  typed-array-length@1.0.4:
    dependencies:
      call-bind: 1.0.2
      for-each: 0.3.3
      is-typed-array: 1.1.10

  typescript@5.0.3: {}

  unbox-primitive@1.0.2:
    dependencies:
      call-bind: 1.0.2
      has-bigints: 1.0.2
      has-symbols: 1.0.3
      which-boxed-primitive: 1.0.2

  undefsafe@2.0.5: {}

  unicode-canonical-property-names-ecmascript@2.0.0: {}

  unicode-match-property-ecmascript@2.0.0:
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.0
      unicode-property-aliases-ecmascript: 2.1.0

  unicode-match-property-value-ecmascript@2.1.0: {}

  unicode-property-aliases-ecmascript@2.1.0: {}

  universalify@0.1.2: {}

  universalify@2.0.0: {}

  unpipe@1.0.0: {}

  update-browserslist-db@1.0.10(browserslist@4.21.5):
    dependencies:
      browserslist: 4.21.5
      escalade: 3.1.1
      picocolors: 1.0.0

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.0

  url@0.10.3:
    dependencies:
      punycode: 1.3.2
      querystring: 0.2.0

  use-sync-external-store@1.2.0(react@18.3.1):
    dependencies:
      react: 18.3.1

  util-deprecate@1.0.2: {}

  util@0.12.5:
    dependencies:
      inherits: 2.0.4
      is-arguments: 1.1.1
      is-generator-function: 1.0.10
      is-typed-array: 1.1.10
      which-typed-array: 1.1.9

  utila@0.4.0: {}

  utils-merge@1.0.1: {}

  uuid@3.4.0: {}

  uuid@8.0.0: {}

  uuid@8.3.2: {}

  v8-compile-cache-lib@3.0.1: {}

  v8-compile-cache@2.3.0: {}

  vary@1.1.2: {}

  vizion@2.2.1:
    dependencies:
      async: 2.6.4
      git-node-fs: 1.0.0(js-git@0.7.8)
      ini: 1.3.8
      js-git: 0.7.8

  vm2@3.9.19:
    dependencies:
      acorn: 8.8.2
      acorn-walk: 8.2.0

  watchpack@2.4.0:
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11

  wbuf@1.7.3:
    dependencies:
      minimalistic-assert: 1.0.1

  webpack-build-notifier@2.3.0:
    dependencies:
      node-notifier: 9.0.1
      strip-ansi: 6.0.1

  webpack-bundle-analyzer@4.8.0:
    dependencies:
      '@discoveryjs/json-ext': 0.5.7
      acorn: 8.8.2
      acorn-walk: 8.2.0
      chalk: 4.1.2
      commander: 7.2.0
      gzip-size: 6.0.0
      lodash: 4.17.21
      opener: 1.5.2
      sirv: 1.0.19
      ws: 7.5.9
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  webpack-cli@5.0.1(webpack-bundle-analyzer@4.8.0)(webpack-dev-server@4.13.2)(webpack@5.78.0):
    dependencies:
      '@discoveryjs/json-ext': 0.5.7
      '@webpack-cli/configtest': 2.0.1(webpack-cli@5.0.1)(webpack@5.78.0)
      '@webpack-cli/info': 2.0.1(webpack-cli@5.0.1)(webpack@5.78.0)
      '@webpack-cli/serve': 2.0.1(webpack-cli@5.0.1)(webpack-dev-server@4.13.2)(webpack@5.78.0)
      colorette: 2.0.19
      commander: 9.5.0
      cross-spawn: 7.0.3
      envinfo: 7.8.1
      fastest-levenshtein: 1.0.16
      import-local: 3.1.0
      interpret: 3.1.1
      rechoir: 0.8.0
      webpack: 5.78.0(webpack-cli@5.0.1)
      webpack-bundle-analyzer: 4.8.0
      webpack-dev-server: 4.13.2(webpack-cli@5.0.1)(webpack@5.78.0)
      webpack-merge: 5.8.0

  webpack-dev-middleware@5.3.3(webpack@5.78.0):
    dependencies:
      colorette: 2.0.19
      memfs: 3.5.0
      mime-types: 2.1.35
      range-parser: 1.2.1
      schema-utils: 4.0.0
      webpack: 5.78.0(webpack-cli@5.0.1)

  webpack-dev-middleware@6.0.2(webpack@5.78.0):
    dependencies:
      colorette: 2.0.19
      memfs: 3.5.0
      mime-types: 2.1.35
      range-parser: 1.2.1
      schema-utils: 4.0.0
      webpack: 5.78.0(webpack-cli@5.0.1)

  webpack-dev-server@4.13.2(webpack-cli@5.0.1)(webpack@5.78.0):
    dependencies:
      '@types/bonjour': 3.5.10
      '@types/connect-history-api-fallback': 1.3.5
      '@types/express': 4.17.17
      '@types/serve-index': 1.9.1
      '@types/serve-static': 1.15.1
      '@types/sockjs': 0.3.33
      '@types/ws': 8.5.4
      ansi-html-community: 0.0.8
      bonjour-service: 1.1.1
      chokidar: 3.5.3
      colorette: 2.0.19
      compression: 1.7.4
      connect-history-api-fallback: 2.0.0
      default-gateway: 6.0.3
      express: 4.18.2
      graceful-fs: 4.2.11
      html-entities: 2.3.3
      http-proxy-middleware: 2.0.6(@types/express@4.17.17)
      ipaddr.js: 2.0.1
      launch-editor: 2.6.0
      open: 8.4.2
      p-retry: 4.6.2
      rimraf: 3.0.2
      schema-utils: 4.0.0
      selfsigned: 2.1.1
      serve-index: 1.9.1
      sockjs: 0.3.24
      spdy: 4.0.2
      webpack: 5.78.0(webpack-cli@5.0.1)
      webpack-cli: 5.0.1(webpack-bundle-analyzer@4.8.0)(webpack-dev-server@4.13.2)(webpack@5.78.0)
      webpack-dev-middleware: 5.3.3(webpack@5.78.0)
      ws: 8.13.0
    transitivePeerDependencies:
      - bufferutil
      - debug
      - supports-color
      - utf-8-validate

  webpack-hot-middleware@2.25.3:
    dependencies:
      ansi-html-community: 0.0.8
      html-entities: 2.3.3
      strip-ansi: 6.0.1

  webpack-manifest-plugin@5.0.0(webpack@5.78.0):
    dependencies:
      tapable: 2.2.1
      webpack: 5.78.0(webpack-cli@5.0.1)
      webpack-sources: 2.3.1

  webpack-merge@5.8.0:
    dependencies:
      clone-deep: 4.0.1
      wildcard: 2.0.0

  webpack-node-externals@3.0.0: {}

  webpack-sources@2.3.1:
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1

  webpack-sources@3.2.3: {}

  webpack@5.78.0(webpack-cli@5.0.1):
    dependencies:
      '@types/eslint-scope': 3.7.4
      '@types/estree': 0.0.51
      '@webassemblyjs/ast': 1.11.1
      '@webassemblyjs/wasm-edit': 1.11.1
      '@webassemblyjs/wasm-parser': 1.11.1
      acorn: 8.8.2
      acorn-import-assertions: 1.8.0(acorn@8.8.2)
      browserslist: 4.21.5
      chrome-trace-event: 1.0.3
      enhanced-resolve: 5.12.0
      es-module-lexer: 0.9.3
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 3.1.1
      tapable: 2.2.1
      terser-webpack-plugin: 5.3.7(webpack@5.78.0)
      watchpack: 2.4.0
      webpack-cli: 5.0.1(webpack-bundle-analyzer@4.8.0)(webpack-dev-server@4.13.2)(webpack@5.78.0)
      webpack-sources: 3.2.3
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js

  webpackbar@5.0.2(webpack@5.78.0):
    dependencies:
      chalk: 4.1.2
      consola: 2.15.3
      pretty-time: 1.1.0
      std-env: 3.3.2
      webpack: 5.78.0(webpack-cli@5.0.1)

  websocket-driver@0.7.4:
    dependencies:
      http-parser-js: 0.5.8
      safe-buffer: 5.2.1
      websocket-extensions: 0.1.4

  websocket-extensions@0.1.4: {}

  which-boxed-primitive@1.0.2:
    dependencies:
      is-bigint: 1.0.4
      is-boolean-object: 1.1.2
      is-number-object: 1.0.7
      is-string: 1.0.7
      is-symbol: 1.0.4

  which-typed-array@1.1.9:
    dependencies:
      available-typed-arrays: 1.0.5
      call-bind: 1.0.2
      for-each: 0.3.3
      gopd: 1.0.1
      has-tostringtag: 1.0.0
      is-typed-array: 1.1.10

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  wildcard@2.0.0: {}

  word-wrap@1.2.3: {}

  wrap-ansi@3.0.1:
    dependencies:
      string-width: 2.1.1
      strip-ansi: 4.0.0

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrappy@1.0.2: {}

  ws@7.4.6: {}

  ws@7.5.9: {}

  ws@8.13.0: {}

  xdg-basedir@4.0.0: {}

  xml2js@0.5.0:
    dependencies:
      sax: 1.2.4
      xmlbuilder: 11.0.1

  xmlbuilder@11.0.1: {}

  xregexp@2.0.0: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yallist@4.0.0: {}

  yaml@1.10.2: {}

  yamljs@0.3.0:
    dependencies:
      argparse: 1.0.10
      glob: 7.2.3

  yargs-parser@21.1.1: {}

  yargs@17.7.1:
    dependencies:
      cliui: 8.0.1
      escalade: 3.1.1
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  ylru@1.3.2: {}

  yn@3.1.1: {}

  yocto-queue@0.1.0: {}
