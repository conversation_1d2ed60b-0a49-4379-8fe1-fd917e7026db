const webpack = require("webpack");
const express = require("express");
const webpackDevMiddleware = require("webpack-dev-middleware");
const webpackHotMiddleware = require("webpack-hot-middleware");
const [clientDev, serverDev] = require("../config/webpack.dev");
const appConstants = require("../config/constants");

const env = { mode: "dev", goal: "local" };

const clientDevConfig = clientDev(env);
const serverDevConfig = serverDev(env);

// 客户端编译
const clientCompiler = webpack(clientDevConfig);
// 服务端编译
const serverCompiler = webpack(serverDevConfig);

// 服务端编译监听，自动重新编译 ssr 相关文件
serverCompiler.watch(
  {
    // 排除 node_modules 目录，避免不必要编译
    ignored: /node_modules/,
  },
  (err) => {
    if (err) throw err;
  }
);

const app = express();

app.use(
  webpackDevMiddleware(clientCompiler, clientDevConfig.devServer.devMiddleware)
);
app.use(webpackHotMiddleware(clientCompiler));

app.listen(appConstants.hmrPort, () => {});
