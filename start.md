# React Custom SSR 项目启动指南

## 项目简介

这是一个自定义的 React 服务端渲染（SSR）项目，专为高并发和大型复杂应用设计。相比 Next.js，该项目针对以下场景进行了优化：

- **高并发场景**：支持 40W QPS（每秒查询数）
- **低延迟要求**：响应时间控制在 100-150ms
- **复杂大型项目**：支持微服务架构和分布式并发渲染
- **高可用性**：支持服务降级、容错和熔断机制

## 技术栈

- **前端框架**：React 18 + TypeScript
- **路由**：React Router Dom 6
- **HTTP 服务**：Koa 2
- **数据获取**：TanStack React Query
- **样式**：Styled Components + Less + TailwindCSS
- **构建工具**：Webpack 5
- **代码分割**：Loadable Components
- **进程管理**：PM2
- **Serverless**：支持 AWS Lambda

## 安装依赖

建议使用 pnpm：

```bash
pnpm install
```

或使用 npm：

```bash
npm install
```

## 开发环境启动

### 启动开发服务器

```bash
npm run dev
```

这个命令会：
1. 启动 Webpack 开发服务器（支持 HMR）
2. 启动 Node.js 服务器（使用 nodemon 自动重启）

开发服务器默认端口配置在环境变量中。

### 启动 Mock 数据服务

```bash
npm run mock
```

这会在 8007 端口启动 JSON Server，提供模拟 API 数据。

## 生产环境部署

### 构建项目

```bash
# 生产环境构建
npm run build

# 或指定环境构建
npm run build:online   # 线上环境
npm run build:beta     # 测试环境  
npm run build:test1    # 测试环境1
```

### 启动生产服务器

```bash
npm start
```

使用 PM2 进程管理器启动服务，配置文件为 `ecosystem.config.js`。

## 项目结构

```
react-custom-ssr/
├── app/                    # 应用核心代码
│   ├── client/            # 客户端入口
│   ├── server/            # 服务端代码
│   └── utils/             # 工具函数
├── src/                   # 源代码
│   ├── apis/              # API 接口
│   ├── pages/             # 页面组件
│   ├── routes/            # 路由配置
│   └── theme/             # 主题样式
├── config/                # 构建配置
├── scripts/               # 脚本文件
└── public/                # 静态资源
```

## 主要特性

1. **服务端渲染（SSR）**：首屏性能优化和 SEO 支持
2. **代码分割**：使用 Loadable Components 实现按需加载
3. **热模块替换**：开发环境支持 HMR
4. **数据预取**：支持 SSR 和 CSR 数据获取
5. **高可用性**：支持服务降级和容错机制
6. **Serverless 部署**：支持边缘渲染和分布式并发渲染

## 环境配置

项目支持多环境配置：
- `local`：本地开发环境
- `test1`：测试环境1
- `beta`：测试环境
- `online`：生产环境

## 注意事项

- 确保 Node.js 版本兼容
- 生产环境建议使用 PM2 进行进程管理
- 支持 Docker 容器化部署
- 支持 AWS Lambda Serverless 部署

## 常用命令

```bash
# 安装依赖
pnpm install

# 开发环境
npm run dev

# 生产构建
npm run build

# 启动生产服务
npm start

# Mock 数据服务
npm run mock
```